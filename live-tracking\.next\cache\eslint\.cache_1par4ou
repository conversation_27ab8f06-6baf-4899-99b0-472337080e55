[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\track\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\LiveMap.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useGeolocation.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useMobileOptimization.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useNetworkStatus.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useSocket.ts": "10"}, {"size": 8041, "mtime": 1751789402649, "results": "11", "hashOfConfig": "12"}, {"size": 2048, "mtime": 1751789898870, "results": "13", "hashOfConfig": "12"}, {"size": 2158, "mtime": 1751789514540, "results": "14", "hashOfConfig": "12"}, {"size": 8303, "mtime": 1751789478909, "results": "15", "hashOfConfig": "12"}, {"size": 2503, "mtime": 1751789563826, "results": "16", "hashOfConfig": "12"}, {"size": 6668, "mtime": 1751789442941, "results": "17", "hashOfConfig": "12"}, {"size": 6128, "mtime": 1751789354583, "results": "18", "hashOfConfig": "12"}, {"size": 5953, "mtime": 1751789781634, "results": "19", "hashOfConfig": "12"}, {"size": 4202, "mtime": 1751789587857, "results": "20", "hashOfConfig": "12"}, {"size": 4283, "mtime": 1751791462054, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "bzu2jo", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx", ["52"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\track\\page.tsx", ["53"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\LiveMap.tsx", ["54"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useGeolocation.ts", ["55", "56"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useMobileOptimization.ts", ["57", "58", "59"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useNetworkStatus.ts", ["60", "61", "62", "63", "64", "65", "66", "67", "68"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\hooks\\useSocket.ts", ["69"], [], {"ruleId": "70", "severity": 2, "message": "71", "line": 17, "column": 5, "nodeType": null, "messageId": "72", "endLine": 17, "endColumn": 11}, {"ruleId": "70", "severity": 2, "message": "73", "line": 75, "column": 9, "nodeType": null, "messageId": "72", "endLine": 75, "endColumn": 19}, {"ruleId": "74", "severity": 2, "message": "75", "line": 9, "column": 37, "nodeType": "76", "messageId": "77", "endLine": 9, "endColumn": 40, "suggestions": "78"}, {"ruleId": "79", "severity": 1, "message": "80", "line": 39, "column": 9, "nodeType": "81", "endLine": 39, "endColumn": 50}, {"ruleId": "79", "severity": 1, "message": "82", "line": 39, "column": 9, "nodeType": "81", "endLine": 39, "endColumn": 50}, {"ruleId": "74", "severity": 2, "message": "75", "line": 87, "column": 43, "nodeType": "76", "messageId": "77", "endLine": 87, "endColumn": 46, "suggestions": "83"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 133, "column": 21, "nodeType": "76", "messageId": "77", "endLine": 133, "endColumn": 24, "suggestions": "84"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 133, "column": 54, "nodeType": "76", "messageId": "77", "endLine": 133, "endColumn": 57, "suggestions": "85"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 21, "column": 38, "nodeType": "76", "messageId": "77", "endLine": 21, "endColumn": 41, "suggestions": "86"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 21, "column": 71, "nodeType": "76", "messageId": "77", "endLine": 21, "endColumn": 74, "suggestions": "87"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 21, "column": 107, "nodeType": "76", "messageId": "77", "endLine": 21, "endColumn": 110, "suggestions": "88"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 96, "column": 40, "nodeType": "76", "messageId": "77", "endLine": 96, "endColumn": 43, "suggestions": "89"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 96, "column": 73, "nodeType": "76", "messageId": "77", "endLine": 96, "endColumn": 76, "suggestions": "90"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 96, "column": 109, "nodeType": "76", "messageId": "77", "endLine": 96, "endColumn": 112, "suggestions": "91"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 121, "column": 42, "nodeType": "76", "messageId": "77", "endLine": 121, "endColumn": 45, "suggestions": "92"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 121, "column": 75, "nodeType": "76", "messageId": "77", "endLine": 121, "endColumn": 78, "suggestions": "93"}, {"ruleId": "74", "severity": 2, "message": "75", "line": 121, "column": 111, "nodeType": "76", "messageId": "77", "endLine": 121, "endColumn": 114, "suggestions": "94"}, {"ruleId": "79", "severity": 1, "message": "95", "line": 105, "column": 6, "nodeType": "96", "endLine": 105, "endColumn": 74, "suggestions": "97"}, "@typescript-eslint/no-unused-vars", "'socket' is assigned a value but never used.", "unusedVar", "'formatTime' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["98", "99"], "react-hooks/exhaustive-deps", "The 'opts' object makes the dependencies of useCallback Hook (at line 131) change on every render. To fix this, wrap the initialization of 'opts' in its own useMemo() Hook.", "VariableDeclarator", "The 'opts' object makes the dependencies of useCallback Hook (at line 168) change on every render. To fix this, wrap the initialization of 'opts' in its own useMemo() Hook.", ["100", "101"], ["102", "103"], ["104", "105"], ["106", "107"], ["108", "109"], ["110", "111"], ["112", "113"], ["114", "115"], ["116", "117"], ["118", "119"], ["120", "121"], ["122", "123"], "React Hook useEffect has a missing dependency: 'handleReconnection'. Either include it or remove the dependency array.", "ArrayExpression", ["124"], {"messageId": "125", "fix": "126", "desc": "127"}, {"messageId": "128", "fix": "129", "desc": "130"}, {"messageId": "125", "fix": "131", "desc": "127"}, {"messageId": "128", "fix": "132", "desc": "130"}, {"messageId": "125", "fix": "133", "desc": "127"}, {"messageId": "128", "fix": "134", "desc": "130"}, {"messageId": "125", "fix": "135", "desc": "127"}, {"messageId": "128", "fix": "136", "desc": "130"}, {"messageId": "125", "fix": "137", "desc": "127"}, {"messageId": "128", "fix": "138", "desc": "130"}, {"messageId": "125", "fix": "139", "desc": "127"}, {"messageId": "128", "fix": "140", "desc": "130"}, {"messageId": "125", "fix": "141", "desc": "127"}, {"messageId": "128", "fix": "142", "desc": "130"}, {"messageId": "125", "fix": "143", "desc": "127"}, {"messageId": "128", "fix": "144", "desc": "130"}, {"messageId": "125", "fix": "145", "desc": "127"}, {"messageId": "128", "fix": "146", "desc": "130"}, {"messageId": "125", "fix": "147", "desc": "127"}, {"messageId": "128", "fix": "148", "desc": "130"}, {"messageId": "125", "fix": "149", "desc": "127"}, {"messageId": "128", "fix": "150", "desc": "130"}, {"messageId": "125", "fix": "151", "desc": "127"}, {"messageId": "128", "fix": "152", "desc": "130"}, {"messageId": "125", "fix": "153", "desc": "127"}, {"messageId": "128", "fix": "154", "desc": "130"}, {"desc": "155", "fix": "156"}, "suggestUnknown", {"range": "157", "text": "158"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "159", "text": "160"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "161", "text": "158"}, {"range": "162", "text": "160"}, {"range": "163", "text": "158"}, {"range": "164", "text": "160"}, {"range": "165", "text": "158"}, {"range": "166", "text": "160"}, {"range": "167", "text": "158"}, {"range": "168", "text": "160"}, {"range": "169", "text": "158"}, {"range": "170", "text": "160"}, {"range": "171", "text": "158"}, {"range": "172", "text": "160"}, {"range": "173", "text": "158"}, {"range": "174", "text": "160"}, {"range": "175", "text": "158"}, {"range": "176", "text": "160"}, {"range": "177", "text": "158"}, {"range": "178", "text": "160"}, {"range": "179", "text": "158"}, {"range": "180", "text": "160"}, {"range": "181", "text": "158"}, {"range": "182", "text": "160"}, {"range": "183", "text": "158"}, {"range": "184", "text": "160"}, "Update the dependencies array to be: [autoConnect, handleReconnection, reconnection, reconnectionAttempts, reconnectionDelay]", {"range": "185", "text": "186"}, [314, 317], "unknown", [314, 317], "never", [2583, 2586], [2583, 2586], [3924, 3927], [3924, 3927], [3957, 3960], [3957, 3960], [569, 572], [569, 572], [602, 605], [602, 605], [638, 641], [638, 641], [2745, 2748], [2745, 2748], [2778, 2781], [2778, 2781], [2814, 2817], [2814, 2817], [3708, 3711], [3708, 3711], [3741, 3744], [3741, 3744], [3777, 3780], [3777, 3780], [2729, 2797], "[autoConnect, handleReconnection, reconnection, reconnectionAttempts, reconnectionDelay]"]