(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2042:(e,r,t)=>{Promise.resolve().then(t.bind(t,5758))},2945:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=t(5239),o=t(8088),s=t(8170),i=t.n(s),a=t(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],m={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3152:(e,r,t)=>{"use strict";t.d(r,{ErrorBoundary:()=>o});var n=t(2907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","ErrorBoundary");(0,n.registerClientReference)(function(){throw Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","useErrorHandler")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4109:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var n=t(7413),o=t(2376),s=t.n(o),i=t(8726),a=t.n(i);t(1135);var l=t(3152);let d={title:"Live Tracking - Real-time Location Sharing",description:"Real-time location tracking application with admin broadcasting and live map visualization"};function c({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Live Tracking"}),(0,n.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]}),(0,n.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:(0,n.jsx)(l.ErrorBoundary,{children:e})})]})}},5758:(e,r,t)=>{"use strict";t.d(r,{ErrorBoundary:()=>s});var n=t(687),o=t(3210);class s extends o.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("Error caught by boundary:",e,r),this.setState({error:e,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,n.jsx)("div",{className:"min-h-screen bg-red-50 flex items-center justify-center p-4",children:(0,n.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-xl p-6",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"The application encountered an unexpected error."}),this.state.error&&(0,n.jsx)("div",{className:"bg-gray-100 rounded p-3 mb-4 text-left",children:(0,n.jsx)("div",{className:"text-sm font-mono text-gray-700",children:this.state.error.message})}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors",children:"Reload Page"})]})})}):this.props.children}}},7538:(e,r,t)=>{Promise.resolve().then(t.bind(t,3152))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9373:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,145],()=>t(2945));module.exports=n})();