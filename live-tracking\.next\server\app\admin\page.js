(()=>{var e={};e.id=698,e.ids=[698],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx","default")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1942:(e,t,r)=>{Promise.resolve().then(r.bind(r,1132))},2042:(e,t,r)=>{Promise.resolve().then(r.bind(r,5758))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3152:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>s});var n=r(2907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","ErrorBoundary");(0,n.registerClientReference)(function(){throw Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","useErrorHandler")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3992:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(3210),s=r(7405);let o=(e={})=>{let{autoConnect:t=!0,reconnection:r=!0,reconnectionAttempts:o=5,reconnectionDelay:i=1e3}=e,[a,c]=(0,n.useState)(null),[l,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)({total:0,admins:0,trackers:0}),p=(0,n.useRef)(),h=(0,n.useRef)(0);(0,n.useEffect)(()=>{if(!t)return;let e=(0,s.io)({reconnection:r,reconnectionAttempts:o,reconnectionDelay:i,transports:["websocket","polling"]});return e.on("connect",()=>{console.log("Socket connected:",e.id),d(!0),h.current=0}),e.on("disconnect",t=>{console.log("Socket disconnected:",t),d(!1),("io server disconnect"===t||"transport close"===t)&&x(e)}),e.on("connect_error",t=>{console.error("Socket connection error:",t),d(!1),x(e)}),e.on("connection-count",e=>{m(e)}),c(e),()=>{p.current&&clearTimeout(p.current),e.disconnect()}},[t,r,o,i]);let x=e=>{if(h.current<o){h.current++;let t=i*Math.pow(2,h.current-1);console.log(`Attempting reconnection ${h.current}/${o} in ${t}ms`),p.current=setTimeout(()=>{e.connected||e.connect()},t)}else console.error("Max reconnection attempts reached")};return{socket:a,isConnected:l,connectionCount:u,joinAsAdmin:()=>{a&&l&&(a.emit("join-as-admin"),console.log("Joined as admin"))},joinAsTracker:()=>{a&&l&&(a.emit("join-as-tracker"),console.log("Joined as tracker"))},sendLocation:e=>{a&&l&&a.emit("location-update",e)},disconnect:()=>{a&&(a.disconnect(),c(null),d(!1))},reconnect:()=>{a&&(h.current=0,a.connect())}}}},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4109:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(7413),s=r(2376),o=r.n(s),i=r(8726),a=r.n(i);r(1135);var c=r(3152);let l={title:"Live Tracking - Real-time Location Sharing",description:"Real-time location tracking application with admin broadcasting and live map visualization"};function d({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Live Tracking"}),(0,n.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]}),(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:(0,n.jsx)(c.ErrorBoundary,{children:e})})]})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var n=r(5239),s=r(8088),o=r(8170),i=r.n(o),a=r(893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(t,c);let l={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5758:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>o});var n=r(687),s=r(3210);class o extends s.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by boundary:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,n.jsx)("div",{className:"min-h-screen bg-red-50 flex items-center justify-center p-4",children:(0,n.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-xl p-6",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"The application encountered an unexpected error."}),this.state.error&&(0,n.jsx)("div",{className:"bg-gray-100 rounded p-3 mb-4 text-left",children:(0,n.jsx)("div",{className:"text-sm font-mono text-gray-700",children:this.state.error.message})}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors",children:"Reload Page"})]})})}):this.props.children}}},7116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(687),s=r(3210),o=r(3992);let i={enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4,updateInterval:5e3,autoStart:!1},a=(e={})=>{let t={...i,...e},[r,n]=(0,s.useState)(null),[o,a]=(0,s.useState)(null),[c,l]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1),m=(0,s.useRef)(null),p=(0,s.useRef)(null),h="geolocation"in navigator,x=(0,s.useCallback)(e=>{n({latitude:e.coords.latitude,longitude:e.coords.longitude,accuracy:e.coords.accuracy,altitude:e.coords.altitude||void 0,altitudeAccuracy:e.coords.altitudeAccuracy||void 0,heading:e.coords.heading||void 0,speed:e.coords.speed||void 0,timestamp:new Date(e.timestamp).toISOString()}),a(null),l(!1)},[]),g=(0,s.useCallback)(e=>{let t={code:e.code,message:v(e.code),timestamp:Date.now()};a(t),l(!1),console.error("Geolocation error:",t)},[]),v=e=>{switch(e){case 1:return"Location access denied by user";case 2:return"Location information unavailable";case 3:return"Location request timeout";default:return"Unknown location error"}},b=(0,s.useCallback)(()=>new Promise((e,r)=>{if(!h)return void r(Error("Geolocation is not supported"));l(!0),a(null),navigator.geolocation.getCurrentPosition(t=>{let r={latitude:t.coords.latitude,longitude:t.coords.longitude,accuracy:t.coords.accuracy,altitude:t.coords.altitude||void 0,altitudeAccuracy:t.coords.altitudeAccuracy||void 0,heading:t.coords.heading||void 0,speed:t.coords.speed||void 0,timestamp:new Date(t.timestamp).toISOString()};n(r),l(!1),e(r)},e=>{g(e),r(e)},{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge})}),[h,t,g]),f=(0,s.useCallback)(()=>{h&&!d&&(u(!0),l(!0),a(null),m.current=navigator.geolocation.watchPosition(x,g,{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge}),t.updateInterval>0&&(p.current=setInterval(()=>{navigator.geolocation.getCurrentPosition(x,e=>{console.warn("Interval location update failed:",e)},{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge})},t.updateInterval)))},[h,d,t,x,g]),j=(0,s.useCallback)(()=>{d&&(u(!1),l(!1),null!==m.current&&(navigator.geolocation.clearWatch(m.current),m.current=null),null!==p.current&&(clearInterval(p.current),p.current=null))},[d]),y=(0,s.useCallback)(()=>{a(null)},[]);return(0,s.useEffect)(()=>(t.autoStart&&h&&f(),()=>{j()}),[t.autoStart,h,f,j]),(0,s.useEffect)(()=>()=>{j()},[j]),{location:r,error:o,isLoading:c,isSupported:h,isTracking:d,startTracking:f,stopTracking:j,getCurrentLocation:b,clearError:y}};function c(){let[e,t]=(0,s.useState)(!1),[r,i]=(0,s.useState)(null),[c,l]=(0,s.useState)({locationsSent:0,lastSentAt:null,startedAt:null}),{socket:d,isConnected:u,connectionCount:m,joinAsAdmin:p,sendLocation:h}=(0,o.F)(),{location:x,error:g,isLoading:v,isSupported:b,isTracking:f,startTracking:j,stopTracking:y,clearError:w}=a({enableHighAccuracy:!0,timeout:1e4,maximumAge:5e3,updateInterval:3e3});(0,s.useRef)("");let k=async()=>{try{if("wakeLock"in navigator){let e=await navigator.wakeLock.request("screen");i(e),console.log("Wake lock acquired"),e.addEventListener("release",()=>{console.log("Wake lock released"),i(null)})}}catch(e){console.error("Failed to acquire wake lock:",e)}},A=()=>{r&&(r.release(),i(null))},C=async()=>{if(!b)return void alert("Geolocation is not supported on this device");try{t(!0),l(e=>({...e,startedAt:new Date,locationsSent:0})),await k(),j()}catch(e){console.error("Failed to start tracking:",e),t(!1)}},P=e=>e?e.toLocaleTimeString():"Never";return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-4",children:(0,n.jsx)("div",{className:"max-w-md mx-auto",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"\uD83D\uDCCD Admin Panel"}),(0,n.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connection"}),(0,n.jsx)("div",{className:`w-3 h-3 rounded-full ${u?"bg-green-500":"bg-red-500"}`})]}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,n.jsxs)("div",{children:["Status: ",u?"Connected":"Disconnected"]}),(0,n.jsxs)("div",{children:["Trackers: ",m.trackers]}),(0,n.jsxs)("div",{children:["Total: ",m.total]})]})]}),(0,n.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Location"}),(0,n.jsx)("div",{className:`w-3 h-3 rounded-full ${f?"bg-green-500":v?"bg-yellow-500":"bg-gray-400"}`})]}),x&&(0,n.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,n.jsxs)("div",{children:["Lat: ",x.latitude.toFixed(6)]}),(0,n.jsxs)("div",{children:["Lng: ",x.longitude.toFixed(6)]}),(0,n.jsxs)("div",{children:["Accuracy: ",x.accuracy?.toFixed(0),"m"]}),(0,n.jsxs)("div",{children:["Updated: ",P(x.timestamp?new Date(x.timestamp):null)]})]}),g&&(0,n.jsxs)("div",{className:"text-xs text-red-500 mt-2",children:["Error: ",g.message,(0,n.jsx)("button",{onClick:w,className:"ml-2 text-blue-500 underline",children:"Clear"})]})]}),(0,n.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Statistics"}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,n.jsxs)("div",{children:["Locations sent: ",c.locationsSent]}),(0,n.jsxs)("div",{children:["Last sent: ",P(c.lastSentAt)]}),(0,n.jsxs)("div",{children:["Running for: ",(e=>{if(!e)return"0s";let t=Math.floor((Date.now()-e.getTime())/1e3),r=Math.floor(t/3600),n=Math.floor(t%3600/60),s=t%60;return r>0?`${r}h ${n}m ${s}s`:n>0?`${n}m ${s}s`:`${s}s`})(c.startedAt)]}),(0,n.jsxs)("div",{children:["Wake lock: ",r?"Active":"Inactive"]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[e?(0,n.jsx)("button",{onClick:()=>{t(!1),y(),A()},className:"w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:"Stop Broadcasting"}):(0,n.jsx)("button",{onClick:C,disabled:!b||!u,className:"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:b?u?"Start Broadcasting":"Connecting...":"Location Not Supported"}),e&&(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"}),"Broadcasting Live"]})})]}),(0,n.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"Instructions:"}),(0,n.jsxs)("ul",{className:"text-xs text-blue-600 space-y-1",children:[(0,n.jsx)("li",{children:"• Keep this page open and active"}),(0,n.jsx)("li",{children:"• Allow location permissions when prompted"}),(0,n.jsx)("li",{children:"• Keep your device charged"}),(0,n.jsx)("li",{children:"• Avoid switching apps frequently"})]})]})]})})})}},7214:(e,t,r)=>{Promise.resolve().then(r.bind(r,7116))},7538:(e,t,r)=>{Promise.resolve().then(r.bind(r,3152))},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9373:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9727:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145,658,405],()=>r(5091));module.exports=n})();