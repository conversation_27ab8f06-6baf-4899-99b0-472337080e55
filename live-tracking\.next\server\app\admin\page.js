/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZG1pbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFqaXRoXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXExpdmUgVHJhY2tpbmdcXFxcbGl2ZS10cmFja2luZ1xcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWppdGhcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcTGl2ZSBUcmFja2luZ1xcbGl2ZS10cmFja2luZ1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\admin\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e07ebe6ba12e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFqaXRoXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXExpdmUgVHJhY2tpbmdcXGxpdmUtdHJhY2tpbmdcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwN2ViZTZiYTEyZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Live Tracking - Real-time Location Sharing\",\n    description: \"Real-time location tracking application with admin broadcasting and live map visualization\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Live Tracking\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),
/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ErrorBoundary = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx",
"ErrorBoundary",
);const useErrorHandler = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx",
"useErrorHandler",
);

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   locationService: () => (/* binding */ locationService),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = 'https://zlmzyxdrojzwzqehslsz.supabase.co';\nconst supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbXp5eGRyb2p6d3pxZWhzbHN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3ODkxMTksImV4cCI6MjA2NzM2NTExOX0.usKASd9XHanrwdhtxsoo0OofC873i_CuJLvdhtNzJqY';\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Location service functions\nconst locationService = {\n    // Insert or update location for a device\n    async upsertLocation (deviceId, location) {\n        const locationData = {\n            device_id: deviceId,\n            latitude: location.latitude,\n            longitude: location.longitude,\n            accuracy: location.accuracy,\n            timestamp: new Date().toISOString()\n        };\n        const { data, error } = await supabase.from('locations').upsert(locationData, {\n            onConflict: 'device_id',\n            ignoreDuplicates: false\n        }).select();\n        if (error) {\n            console.error('Error upserting location:', error);\n            throw error;\n        }\n        return data?.[0];\n    },\n    // Get latest location for a device\n    async getLatestLocation (deviceId) {\n        const { data, error } = await supabase.from('locations').select('*').eq('device_id', deviceId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching latest location:', error);\n            throw error;\n        }\n        return data;\n    },\n    // Get location history for a device\n    async getLocationHistory (deviceId, limit = 100) {\n        const { data, error } = await supabase.from('locations').select('*').eq('device_id', deviceId).order('created_at', {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error('Error fetching location history:', error);\n            throw error;\n        }\n        return data || [];\n    },\n    // Get all active devices (devices with recent locations)\n    async getActiveDevices (minutesThreshold = 30) {\n        const thresholdTime = new Date(Date.now() - minutesThreshold * 60 * 1000).toISOString();\n        const { data, error } = await supabase.from('locations').select('device_id, latitude, longitude, accuracy, timestamp, created_at').gte('created_at', thresholdTime).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching active devices:', error);\n            throw error;\n        }\n        // Group by device_id and get the latest location for each\n        const deviceMap = new Map();\n        data?.forEach((location)=>{\n            if (!deviceMap.has(location.device_id)) {\n                deviceMap.set(location.device_id, location);\n            }\n        });\n        return Array.from(deviceMap.values());\n    },\n    // Subscribe to real-time location updates\n    subscribeToLocationUpdates (callback) {\n        const subscription = supabase.channel('locations').on('postgres_changes', {\n            event: '*',\n            schema: 'public',\n            table: 'locations'\n        }, callback).subscribe();\n        return subscription;\n    },\n    // Subscribe to updates for a specific device\n    subscribeToDeviceUpdates (deviceId, callback) {\n        const subscription = supabase.channel(`device-${deviceId}`).on('postgres_changes', {\n            event: '*',\n            schema: 'public',\n            table: 'locations',\n            filter: `device_id=eq.${deviceId}`\n        }, callback).subscribe();\n        return subscription;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBaml0aCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNMaXZlJTIwVHJhY2tpbmclNUMlNUNsaXZlLXRyYWNraW5nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWppdGglNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDTGl2ZSUyMFRyYWNraW5nJTVDJTVDbGl2ZS10cmFja2luZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBOEs7QUFDOUs7QUFDQSwwT0FBaUw7QUFDakw7QUFDQSwwT0FBaUw7QUFDakw7QUFDQSxvUkFBdU07QUFDdk07QUFDQSx3T0FBZ0w7QUFDaEw7QUFDQSw0UEFBMkw7QUFDM0w7QUFDQSxrUUFBOEw7QUFDOUw7QUFDQSxzUUFBK0wiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFqaXRoXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXExpdmUgVHJhY2tpbmdcXFxcbGl2ZS10cmFja2luZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaml0aFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxMaXZlIFRyYWNraW5nXFxcXGxpdmUtdHJhY2tpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWppdGhcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcTGl2ZSBUcmFja2luZ1xcXFxsaXZlLXRyYWNraW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFqaXRoXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXExpdmUgVHJhY2tpbmdcXFxcbGl2ZS10cmFja2luZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaml0aFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxMaXZlIFRyYWNraW5nXFxcXGxpdmUtdHJhY2tpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBaml0aFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxMaXZlIFRyYWNraW5nXFxcXGxpdmUtdHJhY2tpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWppdGhcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcTGl2ZSBUcmFja2luZ1xcXFxsaXZlLXRyYWNraW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFqaXRoXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXExpdmUgVHJhY2tpbmdcXFxcbGl2ZS10cmFja2luZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(ssr)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FqaXRoJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0xpdmUlMjBUcmFja2luZyU1QyU1Q2xpdmUtdHJhY2tpbmclNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZG1pbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFqaXRoXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXExpdmUgVHJhY2tpbmdcXFxcbGl2ZS10cmFja2luZ1xcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAjith%5C%5CDocuments%5C%5Caugment-projects%5C%5CLive%20Tracking%5C%5Clive-tracking%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSupabaseLocation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSupabaseLocation */ \"(ssr)/./src/hooks/useSupabaseLocation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AdminPage() {\n    const [wakeLock, setWakeLock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deviceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AdminPage.useState\": ()=>`admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    }[\"AdminPage.useState\"]);\n    const { currentLocation, locationHistory, isConnected, isTracking, error, startTracking, stopTracking, lastUpdateTime, updateCount } = (0,_hooks_useSupabaseLocation__WEBPACK_IMPORTED_MODULE_2__.useSupabaseLocation)({\n        deviceId,\n        enableTracking: false,\n        trackingInterval: 3000,\n        enableRealtime: true\n    });\n    // Check geolocation support\n    const isSupported = typeof navigator !== 'undefined' && 'geolocation' in navigator;\n    // Request wake lock to prevent screen from sleeping\n    const requestWakeLock = async ()=>{\n        try {\n            if ('wakeLock' in navigator) {\n                const lock = await navigator.wakeLock.request('screen');\n                setWakeLock(lock);\n                console.log('Wake lock acquired');\n                lock.addEventListener('release', ()=>{\n                    console.log('Wake lock released');\n                    setWakeLock(null);\n                });\n            }\n        } catch (err) {\n            console.error('Failed to acquire wake lock:', err);\n        }\n    };\n    // Release wake lock\n    const releaseWakeLock = ()=>{\n        if (wakeLock) {\n            wakeLock.release();\n            setWakeLock(null);\n        }\n    };\n    const handleStart = async ()=>{\n        if (!isSupported) {\n            alert('Geolocation is not supported on this device');\n            return;\n        }\n        try {\n            await requestWakeLock();\n            startTracking();\n        } catch (err) {\n            console.error('Failed to start tracking:', err);\n        }\n    };\n    const handleStop = ()=>{\n        stopTracking();\n        releaseWakeLock();\n    };\n    const formatTime = (date)=>{\n        if (!date) return 'Never';\n        return date.toLocaleTimeString();\n    };\n    const getTrackingDuration = ()=>{\n        if (!isTracking || !lastUpdateTime) return '0s';\n        const duration = Math.floor((Date.now() - lastUpdateTime.getTime()) / 1000);\n        const hours = Math.floor(duration / 3600);\n        const minutes = Math.floor(duration % 3600 / 60);\n        const seconds = duration % 60;\n        if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;\n        if (minutes > 0) return `${minutes}m ${seconds}s`;\n        return `${seconds}s`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center mb-6 text-gray-800\",\n                        children: \"\\uD83D\\uDCCD Admin Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Supabase Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Status: \",\n                                            isConnected ? 'Connected' : 'Disconnected'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Device ID: \",\n                                            deviceId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Updates sent: \",\n                                            updateCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-3 h-3 rounded-full ${isTracking ? 'bg-green-500' : isConnected ? 'bg-blue-500' : 'bg-gray-400'}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Lat: \",\n                                            currentLocation.latitude.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Lng: \",\n                                            currentLocation.longitude.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Accuracy: \",\n                                            currentLocation.accuracy?.toFixed(0),\n                                            \"m\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Updated: \",\n                                            formatTime(currentLocation.timestamp ? new Date(currentLocation.timestamp) : null)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-500 mt-2\",\n                                children: [\n                                    \"Error: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"Statistics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Locations sent: \",\n                                            updateCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Last sent: \",\n                                            formatTime(lastUpdateTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"History count: \",\n                                            locationHistory.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Wake lock: \",\n                                            wakeLock ? 'Active' : 'Inactive'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            !isTracking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStart,\n                                disabled: !isSupported,\n                                className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors\",\n                                children: !isSupported ? 'Location Not Supported' : 'Start Broadcasting'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStop,\n                                className: \"w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors\",\n                                children: \"Stop Broadcasting\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            isTracking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Broadcasting to Supabase\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Instructions:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-blue-600 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Keep this page open and active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Allow location permissions when prompted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Keep your device charged\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Avoid switching apps frequently\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorHandler auto */ \n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('Error caught by boundary:', error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-red-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-xl p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 text-4xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-800 mb-2\",\n                                children: \"Something went wrong\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"The application encountered an unexpected error.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this),\n                            this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 rounded p-3 mb-4 text-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-mono text-gray-700\",\n                                    children: this.state.error.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors\",\n                                children: \"Reload Page\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n// Hook for error handling in functional components\nconst useErrorHandler = ()=>{\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const handleError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useErrorHandler.useCallback[handleError]\": (error)=>{\n            console.error('Error handled:', error);\n            setError(error);\n        }\n    }[\"useErrorHandler.useCallback[handleError]\"], []);\n    const clearError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback({\n        \"useErrorHandler.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"useErrorHandler.useCallback[clearError]\"], []);\n    // Throw error to be caught by ErrorBoundary\n    if (error) {\n        throw error;\n    }\n    return {\n        handleError,\n        clearError\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSupabaseLocation.ts":
/*!******************************************!*\
  !*** ./src/hooks/useSupabaseLocation.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseLocation: () => (/* binding */ useSupabaseLocation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n\n\nfunction useSupabaseLocation({ deviceId, enableTracking = false, trackingInterval = 5000, enableRealtime = true }) {\n    // State\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [locationHistory, setLocationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isTracking, setIsTracking] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastUpdateTime, setLastUpdateTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [updateCount, setUpdateCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    // Refs\n    const trackingIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const realtimeSubscriptionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const watchIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Send location to Supabase\n    const sendLocation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseLocation.useCallback[sendLocation]\": async (location)=>{\n            try {\n                setError(null);\n                const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.locationService.upsertLocation(deviceId, {\n                    latitude: location.latitude,\n                    longitude: location.longitude,\n                    accuracy: location.accuracy\n                });\n                setCurrentLocation(location);\n                setLastUpdateTime(new Date());\n                setUpdateCount({\n                    \"useSupabaseLocation.useCallback[sendLocation]\": (prev)=>prev + 1\n                }[\"useSupabaseLocation.useCallback[sendLocation]\"]);\n                setIsConnected(true);\n                console.log('Location sent successfully:', result);\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to send location';\n                setError(errorMessage);\n                setIsConnected(false);\n                console.error('Error sending location:', err);\n            }\n        }\n    }[\"useSupabaseLocation.useCallback[sendLocation]\"], [\n        deviceId\n    ]);\n    // Get current position from GPS\n    const getCurrentPosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseLocation.useCallback[getCurrentPosition]\": ()=>{\n            return new Promise({\n                \"useSupabaseLocation.useCallback[getCurrentPosition]\": (resolve, reject)=>{\n                    if (!navigator.geolocation) {\n                        reject(new Error('Geolocation is not supported'));\n                        return;\n                    }\n                    navigator.geolocation.getCurrentPosition({\n                        \"useSupabaseLocation.useCallback[getCurrentPosition]\": (position)=>{\n                            const location = {\n                                latitude: position.coords.latitude,\n                                longitude: position.coords.longitude,\n                                accuracy: position.coords.accuracy,\n                                timestamp: new Date().toISOString()\n                            };\n                            resolve(location);\n                        }\n                    }[\"useSupabaseLocation.useCallback[getCurrentPosition]\"], {\n                        \"useSupabaseLocation.useCallback[getCurrentPosition]\": (error)=>{\n                            reject(new Error(`Geolocation error: ${error.message}`));\n                        }\n                    }[\"useSupabaseLocation.useCallback[getCurrentPosition]\"], {\n                        enableHighAccuracy: true,\n                        timeout: 10000,\n                        maximumAge: 0\n                    });\n                }\n            }[\"useSupabaseLocation.useCallback[getCurrentPosition]\"]);\n        }\n    }[\"useSupabaseLocation.useCallback[getCurrentPosition]\"], []);\n    // Start location tracking\n    const startTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseLocation.useCallback[startTracking]\": async ()=>{\n            if (isTracking) return;\n            try {\n                setError(null);\n                setIsTracking(true);\n                // Send initial location\n                const initialLocation = await getCurrentPosition();\n                await sendLocation(initialLocation);\n                // Set up continuous tracking\n                trackingIntervalRef.current = setInterval({\n                    \"useSupabaseLocation.useCallback[startTracking]\": async ()=>{\n                        try {\n                            const location = await getCurrentPosition();\n                            await sendLocation(location);\n                        } catch (err) {\n                            console.error('Error in tracking interval:', err);\n                            setError(err instanceof Error ? err.message : 'Tracking error');\n                        }\n                    }\n                }[\"useSupabaseLocation.useCallback[startTracking]\"], trackingInterval);\n                console.log('Location tracking started');\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Failed to start tracking');\n                setIsTracking(false);\n                console.error('Error starting tracking:', err);\n            }\n        }\n    }[\"useSupabaseLocation.useCallback[startTracking]\"], [\n        isTracking,\n        getCurrentPosition,\n        sendLocation,\n        trackingInterval\n    ]);\n    // Stop location tracking\n    const stopTracking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseLocation.useCallback[stopTracking]\": ()=>{\n            if (trackingIntervalRef.current) {\n                clearInterval(trackingIntervalRef.current);\n                trackingIntervalRef.current = null;\n            }\n            if (watchIdRef.current !== null) {\n                navigator.geolocation.clearWatch(watchIdRef.current);\n                watchIdRef.current = null;\n            }\n            setIsTracking(false);\n            console.log('Location tracking stopped');\n        }\n    }[\"useSupabaseLocation.useCallback[stopTracking]\"], []);\n    // Load initial data and set up real-time subscription\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseLocation.useEffect\": ()=>{\n            let mounted = true;\n            const initialize = {\n                \"useSupabaseLocation.useEffect.initialize\": async ()=>{\n                    try {\n                        // Load latest location\n                        const latestLocation = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.locationService.getLatestLocation(deviceId);\n                        if (mounted && latestLocation) {\n                            setCurrentLocation({\n                                latitude: latestLocation.latitude,\n                                longitude: latestLocation.longitude,\n                                accuracy: latestLocation.accuracy,\n                                timestamp: latestLocation.timestamp\n                            });\n                        }\n                        // Load location history\n                        const history = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.locationService.getLocationHistory(deviceId, 50);\n                        if (mounted) {\n                            setLocationHistory(history);\n                        }\n                        setIsConnected(true);\n                    } catch (err) {\n                        if (mounted) {\n                            setError(err instanceof Error ? err.message : 'Failed to load initial data');\n                            setIsConnected(false);\n                        }\n                    }\n                }\n            }[\"useSupabaseLocation.useEffect.initialize\"];\n            initialize();\n            // Set up real-time subscription\n            if (enableRealtime) {\n                realtimeSubscriptionRef.current = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.locationService.subscribeToDeviceUpdates(deviceId, {\n                    \"useSupabaseLocation.useEffect\": (payload)=>{\n                        if (!mounted) return;\n                        console.log('Real-time update received:', payload);\n                        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {\n                            const newLocation = payload.new;\n                            setCurrentLocation({\n                                latitude: newLocation.latitude,\n                                longitude: newLocation.longitude,\n                                accuracy: newLocation.accuracy,\n                                timestamp: newLocation.timestamp\n                            });\n                            setLocationHistory({\n                                \"useSupabaseLocation.useEffect\": (prev)=>[\n                                        newLocation,\n                                        ...prev.slice(0, 49)\n                                    ]\n                            }[\"useSupabaseLocation.useEffect\"]);\n                            setLastUpdateTime(new Date());\n                            setIsConnected(true);\n                        }\n                    }\n                }[\"useSupabaseLocation.useEffect\"]);\n            }\n            return ({\n                \"useSupabaseLocation.useEffect\": ()=>{\n                    mounted = false;\n                    if (realtimeSubscriptionRef.current) {\n                        realtimeSubscriptionRef.current.unsubscribe();\n                    }\n                }\n            })[\"useSupabaseLocation.useEffect\"];\n        }\n    }[\"useSupabaseLocation.useEffect\"], [\n        deviceId,\n        enableRealtime\n    ]);\n    // Auto-start tracking if enabled\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseLocation.useEffect\": ()=>{\n            if (enableTracking && !isTracking) {\n                startTracking();\n            }\n        }\n    }[\"useSupabaseLocation.useEffect\"], [\n        enableTracking,\n        isTracking,\n        startTracking\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseLocation.useEffect\": ()=>{\n            return ({\n                \"useSupabaseLocation.useEffect\": ()=>{\n                    stopTracking();\n                    if (realtimeSubscriptionRef.current) {\n                        realtimeSubscriptionRef.current.unsubscribe();\n                    }\n                }\n            })[\"useSupabaseLocation.useEffect\"];\n        }\n    }[\"useSupabaseLocation.useEffect\"], [\n        stopTracking\n    ]);\n    return {\n        currentLocation,\n        locationHistory,\n        isConnected,\n        isTracking,\n        error,\n        startTracking,\n        stopTracking,\n        sendLocation,\n        lastUpdateTime,\n        updateCount\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlU3VwYWJhc2VMb2NhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdFO0FBQ0U7QUFxQzNELFNBQVNLLG9CQUFvQixFQUNsQ0MsUUFBUSxFQUNSQyxpQkFBaUIsS0FBSyxFQUN0QkMsbUJBQW1CLElBQUksRUFDdkJDLGlCQUFpQixJQUFJLEVBQ007SUFFM0IsUUFBUTtJQUNSLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR1gsK0NBQVFBLENBQXVCO0lBQzdFLE1BQU0sQ0FBQ1ksaUJBQWlCQyxtQkFBbUIsR0FBR2IsK0NBQVFBLENBQWlCLEVBQUU7SUFDekUsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dCLFlBQVlDLGNBQWMsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tCLE9BQU9DLFNBQVMsR0FBR25CLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNvQixnQkFBZ0JDLGtCQUFrQixHQUFHckIsK0NBQVFBLENBQWM7SUFDbEUsTUFBTSxDQUFDc0IsYUFBYUMsZUFBZSxHQUFHdkIsK0NBQVFBLENBQUM7SUFFL0MsT0FBTztJQUNQLE1BQU13QixzQkFBc0JyQiw2Q0FBTUEsQ0FBd0I7SUFDMUQsTUFBTXNCLDBCQUEwQnRCLDZDQUFNQSxDQUF5QjtJQUMvRCxNQUFNdUIsYUFBYXZCLDZDQUFNQSxDQUFnQjtJQUV6Qyw0QkFBNEI7SUFDNUIsTUFBTXdCLGVBQWV6QixrREFBV0E7eURBQUMsT0FBTzBCO1lBQ3RDLElBQUk7Z0JBQ0ZULFNBQVM7Z0JBRVQsTUFBTVUsU0FBUyxNQUFNekIsMERBQWVBLENBQUMwQixjQUFjLENBQUN4QixVQUFVO29CQUM1RHlCLFVBQVVILFNBQVNHLFFBQVE7b0JBQzNCQyxXQUFXSixTQUFTSSxTQUFTO29CQUM3QkMsVUFBVUwsU0FBU0ssUUFBUTtnQkFDN0I7Z0JBRUF0QixtQkFBbUJpQjtnQkFDbkJQLGtCQUFrQixJQUFJYTtnQkFDdEJYO3FFQUFlWSxDQUFBQSxPQUFRQSxPQUFPOztnQkFDOUJwQixlQUFlO2dCQUVmcUIsUUFBUUMsR0FBRyxDQUFDLCtCQUErQlI7WUFDN0MsRUFBRSxPQUFPUyxLQUFLO2dCQUNaLE1BQU1DLGVBQWVELGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztnQkFDMUR0QixTQUFTb0I7Z0JBQ1R4QixlQUFlO2dCQUNmcUIsUUFBUWxCLEtBQUssQ0FBQywyQkFBMkJvQjtZQUMzQztRQUNGO3dEQUFHO1FBQUNoQztLQUFTO0lBRWIsZ0NBQWdDO0lBQ2hDLE1BQU1vQyxxQkFBcUJ4QyxrREFBV0E7K0RBQUM7WUFDckMsT0FBTyxJQUFJeUM7dUVBQVEsQ0FBQ0MsU0FBU0M7b0JBQzNCLElBQUksQ0FBQ0MsVUFBVUMsV0FBVyxFQUFFO3dCQUMxQkYsT0FBTyxJQUFJTCxNQUFNO3dCQUNqQjtvQkFDRjtvQkFFQU0sVUFBVUMsV0FBVyxDQUFDTCxrQkFBa0I7K0VBQ3RDLENBQUNNOzRCQUNDLE1BQU1wQixXQUEwQjtnQ0FDOUJHLFVBQVVpQixTQUFTQyxNQUFNLENBQUNsQixRQUFRO2dDQUNsQ0MsV0FBV2dCLFNBQVNDLE1BQU0sQ0FBQ2pCLFNBQVM7Z0NBQ3BDQyxVQUFVZSxTQUFTQyxNQUFNLENBQUNoQixRQUFRO2dDQUNsQ2lCLFdBQVcsSUFBSWhCLE9BQU9pQixXQUFXOzRCQUNuQzs0QkFDQVAsUUFBUWhCO3dCQUNWOzsrRUFDQSxDQUFDVjs0QkFDQzJCLE9BQU8sSUFBSUwsTUFBTSxDQUFDLG1CQUFtQixFQUFFdEIsTUFBTXVCLE9BQU8sRUFBRTt3QkFDeEQ7OEVBQ0E7d0JBQ0VXLG9CQUFvQjt3QkFDcEJDLFNBQVM7d0JBQ1RDLFlBQVk7b0JBQ2Q7Z0JBRUo7O1FBQ0Y7OERBQUcsRUFBRTtJQUVMLDBCQUEwQjtJQUMxQixNQUFNQyxnQkFBZ0JyRCxrREFBV0E7MERBQUM7WUFDaEMsSUFBSWMsWUFBWTtZQUVoQixJQUFJO2dCQUNGRyxTQUFTO2dCQUNURixjQUFjO2dCQUVkLHdCQUF3QjtnQkFDeEIsTUFBTXVDLGtCQUFrQixNQUFNZDtnQkFDOUIsTUFBTWYsYUFBYTZCO2dCQUVuQiw2QkFBNkI7Z0JBQzdCaEMsb0JBQW9CaUMsT0FBTyxHQUFHQztzRUFBWTt3QkFDeEMsSUFBSTs0QkFDRixNQUFNOUIsV0FBVyxNQUFNYzs0QkFDdkIsTUFBTWYsYUFBYUM7d0JBQ3JCLEVBQUUsT0FBT1UsS0FBSzs0QkFDWkYsUUFBUWxCLEtBQUssQ0FBQywrQkFBK0JvQjs0QkFDN0NuQixTQUFTbUIsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO3dCQUNoRDtvQkFDRjtxRUFBR2pDO2dCQUVINEIsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPQyxLQUFLO2dCQUNabkIsU0FBU21CLGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztnQkFDOUN4QixjQUFjO2dCQUNkbUIsUUFBUWxCLEtBQUssQ0FBQyw0QkFBNEJvQjtZQUM1QztRQUNGO3lEQUFHO1FBQUN0QjtRQUFZMEI7UUFBb0JmO1FBQWNuQjtLQUFpQjtJQUVuRSx5QkFBeUI7SUFDekIsTUFBTW1ELGVBQWV6RCxrREFBV0E7eURBQUM7WUFDL0IsSUFBSXNCLG9CQUFvQmlDLE9BQU8sRUFBRTtnQkFDL0JHLGNBQWNwQyxvQkFBb0JpQyxPQUFPO2dCQUN6Q2pDLG9CQUFvQmlDLE9BQU8sR0FBRztZQUNoQztZQUVBLElBQUkvQixXQUFXK0IsT0FBTyxLQUFLLE1BQU07Z0JBQy9CWCxVQUFVQyxXQUFXLENBQUNjLFVBQVUsQ0FBQ25DLFdBQVcrQixPQUFPO2dCQUNuRC9CLFdBQVcrQixPQUFPLEdBQUc7WUFDdkI7WUFFQXhDLGNBQWM7WUFDZG1CLFFBQVFDLEdBQUcsQ0FBQztRQUNkO3dEQUFHLEVBQUU7SUFFTCxzREFBc0Q7SUFDdERwQyxnREFBU0E7eUNBQUM7WUFDUixJQUFJNkQsVUFBVTtZQUVkLE1BQU1DOzREQUFhO29CQUNqQixJQUFJO3dCQUNGLHVCQUF1Qjt3QkFDdkIsTUFBTUMsaUJBQWlCLE1BQU01RCwwREFBZUEsQ0FBQzZELGlCQUFpQixDQUFDM0Q7d0JBQy9ELElBQUl3RCxXQUFXRSxnQkFBZ0I7NEJBQzdCckQsbUJBQW1CO2dDQUNqQm9CLFVBQVVpQyxlQUFlakMsUUFBUTtnQ0FDakNDLFdBQVdnQyxlQUFlaEMsU0FBUztnQ0FDbkNDLFVBQVUrQixlQUFlL0IsUUFBUTtnQ0FDakNpQixXQUFXYyxlQUFlZCxTQUFTOzRCQUNyQzt3QkFDRjt3QkFFQSx3QkFBd0I7d0JBQ3hCLE1BQU1nQixVQUFVLE1BQU05RCwwREFBZUEsQ0FBQytELGtCQUFrQixDQUFDN0QsVUFBVTt3QkFDbkUsSUFBSXdELFNBQVM7NEJBQ1hqRCxtQkFBbUJxRDt3QkFDckI7d0JBRUFuRCxlQUFlO29CQUNqQixFQUFFLE9BQU91QixLQUFLO3dCQUNaLElBQUl3QixTQUFTOzRCQUNYM0MsU0FBU21CLGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRzs0QkFDOUMxQixlQUFlO3dCQUNqQjtvQkFDRjtnQkFDRjs7WUFFQWdEO1lBRUEsZ0NBQWdDO1lBQ2hDLElBQUl0RCxnQkFBZ0I7Z0JBQ2xCZ0Isd0JBQXdCZ0MsT0FBTyxHQUFHckQsMERBQWVBLENBQUNnRSx3QkFBd0IsQ0FDeEU5RDtxREFDQSxDQUFDK0Q7d0JBQ0MsSUFBSSxDQUFDUCxTQUFTO3dCQUVkMUIsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QmdDO3dCQUUxQyxJQUFJQSxRQUFRQyxTQUFTLEtBQUssWUFBWUQsUUFBUUMsU0FBUyxLQUFLLFVBQVU7NEJBQ3BFLE1BQU1DLGNBQWNGLFFBQVFHLEdBQUc7NEJBRS9CN0QsbUJBQW1CO2dDQUNqQm9CLFVBQVV3QyxZQUFZeEMsUUFBUTtnQ0FDOUJDLFdBQVd1QyxZQUFZdkMsU0FBUztnQ0FDaENDLFVBQVVzQyxZQUFZdEMsUUFBUTtnQ0FDOUJpQixXQUFXcUIsWUFBWXJCLFNBQVM7NEJBQ2xDOzRCQUVBckM7aUVBQW1Cc0IsQ0FBQUEsT0FBUTt3Q0FBQ29DOzJDQUFnQnBDLEtBQUtzQyxLQUFLLENBQUMsR0FBRztxQ0FBSTs7NEJBQzlEcEQsa0JBQWtCLElBQUlhOzRCQUN0Qm5CLGVBQWU7d0JBQ2pCO29CQUNGOztZQUVKO1lBRUE7aURBQU87b0JBQ0wrQyxVQUFVO29CQUNWLElBQUlyQyx3QkFBd0JnQyxPQUFPLEVBQUU7d0JBQ25DaEMsd0JBQXdCZ0MsT0FBTyxDQUFDaUIsV0FBVztvQkFDN0M7Z0JBQ0Y7O1FBQ0Y7d0NBQUc7UUFBQ3BFO1FBQVVHO0tBQWU7SUFFN0IsaUNBQWlDO0lBQ2pDUixnREFBU0E7eUNBQUM7WUFDUixJQUFJTSxrQkFBa0IsQ0FBQ1MsWUFBWTtnQkFDakN1QztZQUNGO1FBQ0Y7d0NBQUc7UUFBQ2hEO1FBQWdCUztRQUFZdUM7S0FBYztJQUU5QyxxQkFBcUI7SUFDckJ0RCxnREFBU0E7eUNBQUM7WUFDUjtpREFBTztvQkFDTDBEO29CQUNBLElBQUlsQyx3QkFBd0JnQyxPQUFPLEVBQUU7d0JBQ25DaEMsd0JBQXdCZ0MsT0FBTyxDQUFDaUIsV0FBVztvQkFDN0M7Z0JBQ0Y7O1FBQ0Y7d0NBQUc7UUFBQ2Y7S0FBYTtJQUVqQixPQUFPO1FBQ0xqRDtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBcUM7UUFDQUk7UUFDQWhDO1FBQ0FQO1FBQ0FFO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaml0aFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxMaXZlIFRyYWNraW5nXFxsaXZlLXRyYWNraW5nXFxzcmNcXGhvb2tzXFx1c2VTdXBhYmFzZUxvY2F0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGxvY2F0aW9uU2VydmljZSwgTG9jYXRpb25EYXRhIH0gZnJvbSAnLi4vLi4vbGliL3N1cGFiYXNlJ1xuaW1wb3J0IHsgUmVhbHRpbWVDaGFubmVsIH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuXG5pbnRlcmZhY2UgTG9jYXRpb25TdGF0ZSB7XG4gIGxhdGl0dWRlOiBudW1iZXJcbiAgbG9uZ2l0dWRlOiBudW1iZXJcbiAgYWNjdXJhY3k6IG51bWJlclxuICB0aW1lc3RhbXA6IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgVXNlU3VwYWJhc2VMb2NhdGlvbk9wdGlvbnMge1xuICBkZXZpY2VJZDogc3RyaW5nXG4gIGVuYWJsZVRyYWNraW5nPzogYm9vbGVhblxuICB0cmFja2luZ0ludGVydmFsPzogbnVtYmVyXG4gIGVuYWJsZVJlYWx0aW1lPzogYm9vbGVhblxufVxuXG5pbnRlcmZhY2UgVXNlU3VwYWJhc2VMb2NhdGlvblJldHVybiB7XG4gIC8vIEN1cnJlbnQgbG9jYXRpb24gc3RhdGVcbiAgY3VycmVudExvY2F0aW9uOiBMb2NhdGlvblN0YXRlIHwgbnVsbFxuICBsb2NhdGlvbkhpc3Rvcnk6IExvY2F0aW9uRGF0YVtdXG4gIFxuICAvLyBDb25uZWN0aW9uIHN0YXRlXG4gIGlzQ29ubmVjdGVkOiBib29sZWFuXG4gIGlzVHJhY2tpbmc6IGJvb2xlYW5cbiAgZXJyb3I6IHN0cmluZyB8IG51bGxcbiAgXG4gIC8vIEFjdGlvbnNcbiAgc3RhcnRUcmFja2luZzogKCkgPT4gdm9pZFxuICBzdG9wVHJhY2tpbmc6ICgpID0+IHZvaWRcbiAgc2VuZExvY2F0aW9uOiAobG9jYXRpb246IExvY2F0aW9uU3RhdGUpID0+IFByb21pc2U8dm9pZD5cbiAgXG4gIC8vIFN0YXRzXG4gIGxhc3RVcGRhdGVUaW1lOiBEYXRlIHwgbnVsbFxuICB1cGRhdGVDb3VudDogbnVtYmVyXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VTdXBhYmFzZUxvY2F0aW9uKHtcbiAgZGV2aWNlSWQsXG4gIGVuYWJsZVRyYWNraW5nID0gZmFsc2UsXG4gIHRyYWNraW5nSW50ZXJ2YWwgPSA1MDAwLFxuICBlbmFibGVSZWFsdGltZSA9IHRydWVcbn06IFVzZVN1cGFiYXNlTG9jYXRpb25PcHRpb25zKTogVXNlU3VwYWJhc2VMb2NhdGlvblJldHVybiB7XG4gIFxuICAvLyBTdGF0ZVxuICBjb25zdCBbY3VycmVudExvY2F0aW9uLCBzZXRDdXJyZW50TG9jYXRpb25dID0gdXNlU3RhdGU8TG9jYXRpb25TdGF0ZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2NhdGlvbkhpc3RvcnksIHNldExvY2F0aW9uSGlzdG9yeV0gPSB1c2VTdGF0ZTxMb2NhdGlvbkRhdGFbXT4oW10pXG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1RyYWNraW5nLCBzZXRJc1RyYWNraW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsYXN0VXBkYXRlVGltZSwgc2V0TGFzdFVwZGF0ZVRpbWVdID0gdXNlU3RhdGU8RGF0ZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFt1cGRhdGVDb3VudCwgc2V0VXBkYXRlQ291bnRdID0gdXNlU3RhdGUoMClcbiAgXG4gIC8vIFJlZnNcbiAgY29uc3QgdHJhY2tpbmdJbnRlcnZhbFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IHJlYWx0aW1lU3Vic2NyaXB0aW9uUmVmID0gdXNlUmVmPFJlYWx0aW1lQ2hhbm5lbCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IHdhdGNoSWRSZWYgPSB1c2VSZWY8bnVtYmVyIHwgbnVsbD4obnVsbClcblxuICAvLyBTZW5kIGxvY2F0aW9uIHRvIFN1cGFiYXNlXG4gIGNvbnN0IHNlbmRMb2NhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChsb2NhdGlvbjogTG9jYXRpb25TdGF0ZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKVxuICAgICAgXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBsb2NhdGlvblNlcnZpY2UudXBzZXJ0TG9jYXRpb24oZGV2aWNlSWQsIHtcbiAgICAgICAgbGF0aXR1ZGU6IGxvY2F0aW9uLmxhdGl0dWRlLFxuICAgICAgICBsb25naXR1ZGU6IGxvY2F0aW9uLmxvbmdpdHVkZSxcbiAgICAgICAgYWNjdXJhY3k6IGxvY2F0aW9uLmFjY3VyYWN5XG4gICAgICB9KVxuICAgICAgXG4gICAgICBzZXRDdXJyZW50TG9jYXRpb24obG9jYXRpb24pXG4gICAgICBzZXRMYXN0VXBkYXRlVGltZShuZXcgRGF0ZSgpKVxuICAgICAgc2V0VXBkYXRlQ291bnQocHJldiA9PiBwcmV2ICsgMSlcbiAgICAgIHNldElzQ29ubmVjdGVkKHRydWUpXG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCdMb2NhdGlvbiBzZW50IHN1Y2Nlc3NmdWxseTonLCByZXN1bHQpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBzZW5kIGxvY2F0aW9uJ1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKVxuICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIGxvY2F0aW9uOicsIGVycilcbiAgICB9XG4gIH0sIFtkZXZpY2VJZF0pXG5cbiAgLy8gR2V0IGN1cnJlbnQgcG9zaXRpb24gZnJvbSBHUFNcbiAgY29uc3QgZ2V0Q3VycmVudFBvc2l0aW9uID0gdXNlQ2FsbGJhY2soKCk6IFByb21pc2U8TG9jYXRpb25TdGF0ZT4gPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICBpZiAoIW5hdmlnYXRvci5nZW9sb2NhdGlvbikge1xuICAgICAgICByZWplY3QobmV3IEVycm9yKCdHZW9sb2NhdGlvbiBpcyBub3Qgc3VwcG9ydGVkJykpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBuYXZpZ2F0b3IuZ2VvbG9jYXRpb24uZ2V0Q3VycmVudFBvc2l0aW9uKFxuICAgICAgICAocG9zaXRpb24pID0+IHtcbiAgICAgICAgICBjb25zdCBsb2NhdGlvbjogTG9jYXRpb25TdGF0ZSA9IHtcbiAgICAgICAgICAgIGxhdGl0dWRlOiBwb3NpdGlvbi5jb29yZHMubGF0aXR1ZGUsXG4gICAgICAgICAgICBsb25naXR1ZGU6IHBvc2l0aW9uLmNvb3Jkcy5sb25naXR1ZGUsXG4gICAgICAgICAgICBhY2N1cmFjeTogcG9zaXRpb24uY29vcmRzLmFjY3VyYWN5LFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICB9XG4gICAgICAgICAgcmVzb2x2ZShsb2NhdGlvbilcbiAgICAgICAgfSxcbiAgICAgICAgKGVycm9yKSA9PiB7XG4gICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcihgR2VvbG9jYXRpb24gZXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gKSlcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGVuYWJsZUhpZ2hBY2N1cmFjeTogdHJ1ZSxcbiAgICAgICAgICB0aW1lb3V0OiAxMDAwMCxcbiAgICAgICAgICBtYXhpbXVtQWdlOiAwXG4gICAgICAgIH1cbiAgICAgIClcbiAgICB9KVxuICB9LCBbXSlcblxuICAvLyBTdGFydCBsb2NhdGlvbiB0cmFja2luZ1xuICBjb25zdCBzdGFydFRyYWNraW5nID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmIChpc1RyYWNraW5nKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKVxuICAgICAgc2V0SXNUcmFja2luZyh0cnVlKVxuXG4gICAgICAvLyBTZW5kIGluaXRpYWwgbG9jYXRpb25cbiAgICAgIGNvbnN0IGluaXRpYWxMb2NhdGlvbiA9IGF3YWl0IGdldEN1cnJlbnRQb3NpdGlvbigpXG4gICAgICBhd2FpdCBzZW5kTG9jYXRpb24oaW5pdGlhbExvY2F0aW9uKVxuXG4gICAgICAvLyBTZXQgdXAgY29udGludW91cyB0cmFja2luZ1xuICAgICAgdHJhY2tpbmdJbnRlcnZhbFJlZi5jdXJyZW50ID0gc2V0SW50ZXJ2YWwoYXN5bmMgKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGxvY2F0aW9uID0gYXdhaXQgZ2V0Q3VycmVudFBvc2l0aW9uKClcbiAgICAgICAgICBhd2FpdCBzZW5kTG9jYXRpb24obG9jYXRpb24pXG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHRyYWNraW5nIGludGVydmFsOicsIGVycilcbiAgICAgICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1RyYWNraW5nIGVycm9yJylcbiAgICAgICAgfVxuICAgICAgfSwgdHJhY2tpbmdJbnRlcnZhbClcblxuICAgICAgY29uc29sZS5sb2coJ0xvY2F0aW9uIHRyYWNraW5nIHN0YXJ0ZWQnKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gc3RhcnQgdHJhY2tpbmcnKVxuICAgICAgc2V0SXNUcmFja2luZyhmYWxzZSlcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0YXJ0aW5nIHRyYWNraW5nOicsIGVycilcbiAgICB9XG4gIH0sIFtpc1RyYWNraW5nLCBnZXRDdXJyZW50UG9zaXRpb24sIHNlbmRMb2NhdGlvbiwgdHJhY2tpbmdJbnRlcnZhbF0pXG5cbiAgLy8gU3RvcCBsb2NhdGlvbiB0cmFja2luZ1xuICBjb25zdCBzdG9wVHJhY2tpbmcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKHRyYWNraW5nSW50ZXJ2YWxSZWYuY3VycmVudCkge1xuICAgICAgY2xlYXJJbnRlcnZhbCh0cmFja2luZ0ludGVydmFsUmVmLmN1cnJlbnQpXG4gICAgICB0cmFja2luZ0ludGVydmFsUmVmLmN1cnJlbnQgPSBudWxsXG4gICAgfVxuICAgIFxuICAgIGlmICh3YXRjaElkUmVmLmN1cnJlbnQgIT09IG51bGwpIHtcbiAgICAgIG5hdmlnYXRvci5nZW9sb2NhdGlvbi5jbGVhcldhdGNoKHdhdGNoSWRSZWYuY3VycmVudClcbiAgICAgIHdhdGNoSWRSZWYuY3VycmVudCA9IG51bGxcbiAgICB9XG4gICAgXG4gICAgc2V0SXNUcmFja2luZyhmYWxzZSlcbiAgICBjb25zb2xlLmxvZygnTG9jYXRpb24gdHJhY2tpbmcgc3RvcHBlZCcpXG4gIH0sIFtdKVxuXG4gIC8vIExvYWQgaW5pdGlhbCBkYXRhIGFuZCBzZXQgdXAgcmVhbC10aW1lIHN1YnNjcmlwdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBtb3VudGVkID0gdHJ1ZVxuXG4gICAgY29uc3QgaW5pdGlhbGl6ZSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIExvYWQgbGF0ZXN0IGxvY2F0aW9uXG4gICAgICAgIGNvbnN0IGxhdGVzdExvY2F0aW9uID0gYXdhaXQgbG9jYXRpb25TZXJ2aWNlLmdldExhdGVzdExvY2F0aW9uKGRldmljZUlkKVxuICAgICAgICBpZiAobW91bnRlZCAmJiBsYXRlc3RMb2NhdGlvbikge1xuICAgICAgICAgIHNldEN1cnJlbnRMb2NhdGlvbih7XG4gICAgICAgICAgICBsYXRpdHVkZTogbGF0ZXN0TG9jYXRpb24ubGF0aXR1ZGUsXG4gICAgICAgICAgICBsb25naXR1ZGU6IGxhdGVzdExvY2F0aW9uLmxvbmdpdHVkZSxcbiAgICAgICAgICAgIGFjY3VyYWN5OiBsYXRlc3RMb2NhdGlvbi5hY2N1cmFjeSxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbGF0ZXN0TG9jYXRpb24udGltZXN0YW1wXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIExvYWQgbG9jYXRpb24gaGlzdG9yeVxuICAgICAgICBjb25zdCBoaXN0b3J5ID0gYXdhaXQgbG9jYXRpb25TZXJ2aWNlLmdldExvY2F0aW9uSGlzdG9yeShkZXZpY2VJZCwgNTApXG4gICAgICAgIGlmIChtb3VudGVkKSB7XG4gICAgICAgICAgc2V0TG9jYXRpb25IaXN0b3J5KGhpc3RvcnkpXG4gICAgICAgIH1cblxuICAgICAgICBzZXRJc0Nvbm5lY3RlZCh0cnVlKVxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGlmIChtb3VudGVkKSB7XG4gICAgICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBpbml0aWFsIGRhdGEnKVxuICAgICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaW5pdGlhbGl6ZSgpXG5cbiAgICAvLyBTZXQgdXAgcmVhbC10aW1lIHN1YnNjcmlwdGlvblxuICAgIGlmIChlbmFibGVSZWFsdGltZSkge1xuICAgICAgcmVhbHRpbWVTdWJzY3JpcHRpb25SZWYuY3VycmVudCA9IGxvY2F0aW9uU2VydmljZS5zdWJzY3JpYmVUb0RldmljZVVwZGF0ZXMoXG4gICAgICAgIGRldmljZUlkLFxuICAgICAgICAocGF5bG9hZCkgPT4ge1xuICAgICAgICAgIGlmICghbW91bnRlZCkgcmV0dXJuXG5cbiAgICAgICAgICBjb25zb2xlLmxvZygnUmVhbC10aW1lIHVwZGF0ZSByZWNlaXZlZDonLCBwYXlsb2FkKVxuICAgICAgICAgIFxuICAgICAgICAgIGlmIChwYXlsb2FkLmV2ZW50VHlwZSA9PT0gJ0lOU0VSVCcgfHwgcGF5bG9hZC5ldmVudFR5cGUgPT09ICdVUERBVEUnKSB7XG4gICAgICAgICAgICBjb25zdCBuZXdMb2NhdGlvbiA9IHBheWxvYWQubmV3IGFzIExvY2F0aW9uRGF0YVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBzZXRDdXJyZW50TG9jYXRpb24oe1xuICAgICAgICAgICAgICBsYXRpdHVkZTogbmV3TG9jYXRpb24ubGF0aXR1ZGUsXG4gICAgICAgICAgICAgIGxvbmdpdHVkZTogbmV3TG9jYXRpb24ubG9uZ2l0dWRlLFxuICAgICAgICAgICAgICBhY2N1cmFjeTogbmV3TG9jYXRpb24uYWNjdXJhY3ksXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3TG9jYXRpb24udGltZXN0YW1wXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBzZXRMb2NhdGlvbkhpc3RvcnkocHJldiA9PiBbbmV3TG9jYXRpb24sIC4uLnByZXYuc2xpY2UoMCwgNDkpXSlcbiAgICAgICAgICAgIHNldExhc3RVcGRhdGVUaW1lKG5ldyBEYXRlKCkpXG4gICAgICAgICAgICBzZXRJc0Nvbm5lY3RlZCh0cnVlKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBtb3VudGVkID0gZmFsc2VcbiAgICAgIGlmIChyZWFsdGltZVN1YnNjcmlwdGlvblJlZi5jdXJyZW50KSB7XG4gICAgICAgIHJlYWx0aW1lU3Vic2NyaXB0aW9uUmVmLmN1cnJlbnQudW5zdWJzY3JpYmUoKVxuICAgICAgfVxuICAgIH1cbiAgfSwgW2RldmljZUlkLCBlbmFibGVSZWFsdGltZV0pXG5cbiAgLy8gQXV0by1zdGFydCB0cmFja2luZyBpZiBlbmFibGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGVuYWJsZVRyYWNraW5nICYmICFpc1RyYWNraW5nKSB7XG4gICAgICBzdGFydFRyYWNraW5nKClcbiAgICB9XG4gIH0sIFtlbmFibGVUcmFja2luZywgaXNUcmFja2luZywgc3RhcnRUcmFja2luZ10pXG5cbiAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHN0b3BUcmFja2luZygpXG4gICAgICBpZiAocmVhbHRpbWVTdWJzY3JpcHRpb25SZWYuY3VycmVudCkge1xuICAgICAgICByZWFsdGltZVN1YnNjcmlwdGlvblJlZi5jdXJyZW50LnVuc3Vic2NyaWJlKClcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdG9wVHJhY2tpbmddKVxuXG4gIHJldHVybiB7XG4gICAgY3VycmVudExvY2F0aW9uLFxuICAgIGxvY2F0aW9uSGlzdG9yeSxcbiAgICBpc0Nvbm5lY3RlZCxcbiAgICBpc1RyYWNraW5nLFxuICAgIGVycm9yLFxuICAgIHN0YXJ0VHJhY2tpbmcsXG4gICAgc3RvcFRyYWNraW5nLFxuICAgIHNlbmRMb2NhdGlvbixcbiAgICBsYXN0VXBkYXRlVGltZSxcbiAgICB1cGRhdGVDb3VudFxuICB9XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsImxvY2F0aW9uU2VydmljZSIsInVzZVN1cGFiYXNlTG9jYXRpb24iLCJkZXZpY2VJZCIsImVuYWJsZVRyYWNraW5nIiwidHJhY2tpbmdJbnRlcnZhbCIsImVuYWJsZVJlYWx0aW1lIiwiY3VycmVudExvY2F0aW9uIiwic2V0Q3VycmVudExvY2F0aW9uIiwibG9jYXRpb25IaXN0b3J5Iiwic2V0TG9jYXRpb25IaXN0b3J5IiwiaXNDb25uZWN0ZWQiLCJzZXRJc0Nvbm5lY3RlZCIsImlzVHJhY2tpbmciLCJzZXRJc1RyYWNraW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImxhc3RVcGRhdGVUaW1lIiwic2V0TGFzdFVwZGF0ZVRpbWUiLCJ1cGRhdGVDb3VudCIsInNldFVwZGF0ZUNvdW50IiwidHJhY2tpbmdJbnRlcnZhbFJlZiIsInJlYWx0aW1lU3Vic2NyaXB0aW9uUmVmIiwid2F0Y2hJZFJlZiIsInNlbmRMb2NhdGlvbiIsImxvY2F0aW9uIiwicmVzdWx0IiwidXBzZXJ0TG9jYXRpb24iLCJsYXRpdHVkZSIsImxvbmdpdHVkZSIsImFjY3VyYWN5IiwiRGF0ZSIsInByZXYiLCJjb25zb2xlIiwibG9nIiwiZXJyIiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJtZXNzYWdlIiwiZ2V0Q3VycmVudFBvc2l0aW9uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJuYXZpZ2F0b3IiLCJnZW9sb2NhdGlvbiIsInBvc2l0aW9uIiwiY29vcmRzIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJlbmFibGVIaWdoQWNjdXJhY3kiLCJ0aW1lb3V0IiwibWF4aW11bUFnZSIsInN0YXJ0VHJhY2tpbmciLCJpbml0aWFsTG9jYXRpb24iLCJjdXJyZW50Iiwic2V0SW50ZXJ2YWwiLCJzdG9wVHJhY2tpbmciLCJjbGVhckludGVydmFsIiwiY2xlYXJXYXRjaCIsIm1vdW50ZWQiLCJpbml0aWFsaXplIiwibGF0ZXN0TG9jYXRpb24iLCJnZXRMYXRlc3RMb2NhdGlvbiIsImhpc3RvcnkiLCJnZXRMb2NhdGlvbkhpc3RvcnkiLCJzdWJzY3JpYmVUb0RldmljZVVwZGF0ZXMiLCJwYXlsb2FkIiwiZXZlbnRUeXBlIiwibmV3TG9jYXRpb24iLCJuZXciLCJzbGljZSIsInVuc3Vic2NyaWJlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSupabaseLocation.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ws","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAjith%5CDocuments%5Caugment-projects%5CLive%20Tracking%5Clive-tracking&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();