(()=>{var e={};e.id=970,e.ids=[970],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(5239),n=r(8088),i=r(8170),o=r.n(i),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["track",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2828)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\track\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\track\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/track/page",pathname:"/track",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1135:()=>{},1606:(e,t,r)=>{Promise.resolve().then(r.bind(r,2828))},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1968:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},2042:(e,t,r)=>{Promise.resolve().then(r.bind(r,5758))},2828:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\app\\track\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3152:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>n});var s=r(2907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","ErrorBoundary");(0,s.registerClientReference)(function(){throw Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Live Tracking\\live-tracking\\src\\components\\ErrorBoundary.tsx","useErrorHandler")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3734:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(687),n=r(3210),i=r(9587),o=r.n(i),a=r(3992);let l=o()(async()=>{},{loadableGenerated:{modules:["app\\track\\page.tsx -> @/components/LiveMap"]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-gray-600",children:"Loading map..."})]})})});function c(){let[e,t]=(0,n.useState)(null),[r,i]=(0,n.useState)([]),[o,c]=(0,n.useState)(!0),[d,u]=(0,n.useState)(!0),[m,p]=(0,n.useState)(!0),[x,h]=(0,n.useState)(null),{socket:f,isConnected:g,connectionCount:b,joinAsTracker:v}=(0,a.F)();return(0,s.jsxs)("div",{className:"h-screen flex flex-col bg-gray-100",children:[(0,s.jsxs)("div",{className:"bg-white shadow-sm border-b p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"\uD83D\uDCCD Live Tracking"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${g?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:g?"Connected":"Disconnected"})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Admins: ",b.admins]})]})]}),(0,s.jsxs)("div",{className:"mt-3 flex items-center justify-between text-sm text-gray-500",children:[(0,s.jsxs)("div",{children:["Last update: ",(()=>{if(!x)return"Never";let e=Math.floor((Date.now()-x.getTime())/1e3);if(e<60)return`${e}s ago`;let t=Math.floor(e/60);if(t<60)return`${t}m ago`;let r=Math.floor(t/60);return`${r}h ago`})()]}),(0,s.jsxs)("div",{children:["History: ",r.length," points"]})]})]}),(0,s.jsx)("div",{className:"bg-white border-b p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:o,onChange:e=>c(e.target.checked),className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Auto Center"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:d,onChange:e=>u(e.target.checked),className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Show History"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:m,onChange:e=>p(e.target.checked),className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Show Path"})]})]}),(0,s.jsx)("button",{onClick:()=>{i([])},className:"px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors",children:"Clear History"})]})}),(0,s.jsxs)("div",{className:"flex-1 relative",children:[!g&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[2000]",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-gray-800 font-medium",children:"Connecting to server..."}),(0,s.jsx)("div",{className:"text-gray-600 text-sm mt-2",children:"Make sure the admin is broadcasting"})]})}),g&&0===b.admins&&(0,s.jsx)("div",{className:"absolute inset-0 bg-yellow-50 flex items-center justify-center z-[2000]",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 text-center border border-yellow-200",children:[(0,s.jsx)("div",{className:"text-yellow-600 text-4xl mb-4",children:"⚠️"}),(0,s.jsx)("div",{className:"text-gray-800 font-medium",children:"No admin broadcasting"}),(0,s.jsx)("div",{className:"text-gray-600 text-sm mt-2",children:"Waiting for admin to start location sharing..."})]})}),(0,s.jsx)(l,{currentLocation:e,locationHistory:r,autoCenter:o,showHistory:d,showPath:m,className:"w-full h-full"})]}),e&&(0,s.jsx)("div",{className:"bg-white border-t p-3",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-500",children:"Latitude"}),(0,s.jsx)("div",{className:"font-mono",children:e.latitude.toFixed(6)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-500",children:"Longitude"}),(0,s.jsx)("div",{className:"font-mono",children:e.longitude.toFixed(6)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-500",children:"Accuracy"}),(0,s.jsx)("div",{className:"font-mono",children:e.accuracy?`\xb1${e.accuracy.toFixed(0)}m`:"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-500",children:"Speed"}),(0,s.jsx)("div",{className:"font-mono",children:e.speed?`${(3.6*e.speed).toFixed(1)} km/h`:"N/A"})]})]})})]})}},3873:e=>{"use strict";e.exports=require("path")},3992:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(3210),n=r(7405);let i=(e={})=>{let{autoConnect:t=!0,reconnection:r=!0,reconnectionAttempts:i=5,reconnectionDelay:o=1e3}=e,[a,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!1),[u,m]=(0,s.useState)({total:0,admins:0,trackers:0}),p=(0,s.useRef)(null),x=(0,s.useRef)(0);(0,s.useEffect)(()=>{if(!t)return;let e=(0,n.io)({reconnection:r,reconnectionAttempts:i,reconnectionDelay:o,transports:["websocket","polling"]});return e.on("connect",()=>{console.log("Socket connected:",e.id),d(!0),x.current=0}),e.on("disconnect",t=>{console.log("Socket disconnected:",t),d(!1),("io server disconnect"===t||"transport close"===t)&&h(e)}),e.on("connect_error",t=>{console.error("Socket connection error:",t),d(!1),h(e)}),e.on("connection-count",e=>{m(e)}),l(e),()=>{p.current&&clearTimeout(p.current),e.disconnect()}},[t,r,i,o]);let h=e=>{if(x.current<i){x.current++;let t=o*Math.pow(2,x.current-1);console.log(`Attempting reconnection ${x.current}/${i} in ${t}ms`),p.current=setTimeout(()=>{e.connected||e.connect()},t)}else console.error("Max reconnection attempts reached")};return{socket:a,isConnected:c,connectionCount:u,joinAsAdmin:()=>{a&&c&&(a.emit("join-as-admin"),console.log("Joined as admin"))},joinAsTracker:()=>{a&&c&&(a.emit("join-as-tracker"),console.log("Joined as tracker"))},sendLocation:e=>{a&&c&&a.emit("location-update",e)},disconnect:()=>{a&&(a.disconnect(),l(null),d(!1))},reconnect:()=>{a&&(x.current=0,a.connect())}}}},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4109:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var s=r(7413),n=r(2376),i=r.n(n),o=r(8726),a=r.n(o);r(1135);var l=r(3152);let c={title:"Live Tracking - Real-time Location Sharing",description:"Real-time location tracking application with admin broadcasting and live map visualization"};function d({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,s.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Live Tracking"}),(0,s.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]}),(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsx)(l.ErrorBoundary,{children:e})})]})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}});let s=r(687),n=r(1215),i=r(9294),o=r(1968);function a(e){let{moduleIds:t}=e,r=i.workAsyncStorage.getStore();if(void 0===r)return null;let a=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;a.push(...t)}}return 0===a.length?null:(0,s.jsx)(s.Fragment,{children:a.map(e=>{let t=r.assetPrefix+"/_next/"+(0,o.encodeURIPath)(e);return e.endsWith(".css")?(0,s.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,n.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},4814:(e,t,r)=>{Promise.resolve().then(r.bind(r,3734))},4963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let s=r(687),n=r(3210),i=r(6780),o=r(4777);function a(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},r=(0,n.lazy)(()=>t.loader().then(a)),c=t.loading;function d(e){let a=c?(0,s.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,d=l?n.Suspense:n.Fragment,u=t.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.PreloadChunks,{moduleIds:t.modules}),(0,s.jsx)(r,{...e})]}):(0,s.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(d,{...l?{fallback:a}:{},children:u})}return d.displayName="LoadableComponent",d}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5758:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>i});var s=r(687),n=r(3210);class i extends n.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by boundary:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-screen bg-red-50 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-xl p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"The application encountered an unexpected error."}),this.state.error&&(0,s.jsx)("div",{className:"bg-gray-100 rounded p-3 mb-4 text-left",children:(0,s.jsx)("div",{className:"text-sm font-mono text-gray-700",children:this.state.error.message})}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors",children:"Reload Page"})]})})}):this.props.children}}},6780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let s=r(1208);function n(e){let{reason:t,children:r}=e;throw Object.defineProperty(new s.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},7538:(e,t,r)=>{Promise.resolve().then(r.bind(r,3152))},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9373:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=r(4985)._(r(4963));function n(e,t){var r;let n={};"function"==typeof e&&(n.loader=e);let i={...n,...t};return(0,s.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9646:e=>{"use strict";e.exports=require("child_process")},9727:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,658,405],()=>r(1075));module.exports=s})();