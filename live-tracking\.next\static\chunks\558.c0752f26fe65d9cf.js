"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{6177:(e,n,t)=>{t.r(n),t.d(n,{default:()=>k});var r=t(5155),o=t(2115);function i(e,n){return Object.freeze({...e,...n})}let l=(0,o.createContext)(null);function c(){let e=(0,o.use)(l);if(null==e)throw Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return e}var a=t(5752),u=t.n(a);let s=(0,o.forwardRef)(function({bounds:e,boundsOptions:n,center:t,children:r,className:i,id:c,placeholder:u,style:s,whenReady:d,zoom:f,...p},g){let[m]=(0,o.useState)({className:i,id:c,style:s}),[x,h]=(0,o.useState)(null),v=(0,o.useRef)(void 0);(0,o.useImperativeHandle)(g,()=>x?.map??null,[x]);let y=(0,o.useCallback)(r=>{if(null!==r&&!v.current){let o=new a.Map(r,p);v.current=o,null!=t&&null!=f?o.setView(t,f):null!=e&&o.fitBounds(e,n),null!=d&&o.whenReady(d),h(Object.freeze({__version:1,map:o}))}},[]);(0,o.useEffect)(()=>()=>{x?.map.remove()},[x]);let w=x?o.createElement(l,{value:x},r):u??null;return o.createElement("div",{...m,ref:y},w)});var d=t(7650);function f(e){return(0,o.forwardRef)(function(n,t){let{instance:r,context:i}=e(n).current;(0,o.useImperativeHandle)(t,()=>r);let{children:c}=n;return null==c?null:o.createElement(l,{value:i},c)})}function p(e,n){let t=(0,o.useRef)(n);(0,o.useEffect)(function(){n!==t.current&&null!=e.attributionControl&&(null!=t.current&&e.attributionControl.removeAttribution(t.current),null!=n&&e.attributionControl.addAttribution(n)),t.current=n},[e,n])}function g(e,n){let t=(0,o.useRef)(void 0);(0,o.useEffect)(function(){return null!=n&&e.instance.on(n),t.current=n,function(){null!=t.current&&e.instance.off(t.current),t.current=null}},[e,n])}function m(e,n){let t=e.pane??n.pane;return t?{...e,pane:t}:e}function x(e,n,t){return Object.freeze({instance:e,context:n,container:t})}function h(e,n){return null==n?function(n,t){let r=(0,o.useRef)(void 0);return r.current||(r.current=e(n,t)),r}:function(t,r){let i=(0,o.useRef)(void 0);i.current||(i.current=e(t,r));let l=(0,o.useRef)(t),{instance:c}=i.current;return(0,o.useEffect)(function(){l.current!==t&&(n(c,t,l.current),l.current=t)},[c,t,n]),i}}function v(e,n){(0,o.useEffect)(function(){return(n.layerContainer??n.map).addLayer(e.instance),function(){n.layerContainer?.removeLayer(e.instance),n.map.removeLayer(e.instance)}},[n,e])}function y(e){return function(n){let t=c(),r=e(m(n,t),t);return p(t.map,n.attribution),g(r.current,n.eventHandlers),v(r.current,t),r}}let w=function(e,n){var t;return t=y(h(e,n)),(0,o.forwardRef)(function(e,n){let{instance:r}=t(e).current;return(0,o.useImperativeHandle)(n,()=>r),null})}(function({url:e,...n},t){return x(new a.TileLayer(e,m(n,t)),t)},function(e,n,t){let{opacity:r,zIndex:o}=n;null!=r&&r!==t.opacity&&e.setOpacity(r),null!=o&&o!==t.zIndex&&e.setZIndex(o);let{url:i}=n;null!=i&&i!==t.url&&e.setUrl(i)}),b=f(y(h(function({position:e,...n},t){let r=new a.Marker(e,n);return x(r,i(t,{overlayContainer:r}))},function(e,n,t){n.position!==t.position&&e.setLatLng(n.position),null!=n.icon&&n.icon!==t.icon&&e.setIcon(n.icon),null!=n.zIndexOffset&&n.zIndexOffset!==t.zIndexOffset&&e.setZIndexOffset(n.zIndexOffset),null!=n.opacity&&n.opacity!==t.opacity&&e.setOpacity(n.opacity),null!=e.dragging&&n.draggable!==t.draggable&&(!0===n.draggable?e.dragging.enable():e.dragging.disable())}))),F=function(e,n){var t,r;return t=h(e),r=function(e,r){let o=c(),i=t(m(e,o),o);return p(o.map,e.attribution),g(i.current,e.eventHandlers),n(i.current,o,e,r),i},(0,o.forwardRef)(function(e,n){let[t,i]=(0,o.useState)(!1),{instance:l}=r(e,i).current;(0,o.useImperativeHandle)(n,()=>l),(0,o.useEffect)(function(){t&&l.update()},[l,t,e.children]);let c=l._contentNode;return c?(0,d.createPortal)(e.children,c):null})}(function(e,n){return x(new a.Popup(e,n.overlayContainer),n)},function(e,n,{position:t},r){(0,o.useEffect)(function(){let{instance:o}=e;function i(e){e.popup===o&&(o.update(),r(!0))}function l(e){e.popup===o&&r(!1)}return n.map.on({popupopen:i,popupclose:l}),null==n.overlayContainer?(null!=t&&o.setLatLng(t),o.openOn(n.map)):n.overlayContainer.bindPopup(o),function(){n.map.off({popupopen:i,popupclose:l}),n.overlayContainer?.unbindPopup(),n.map.removeLayer(o)}},[e,n,r,t])}),j=function(e,n){var t;return f((t=h(e,n),function(e){let n=c(),r=t(m(e,n),n);g(r.current,e.eventHandlers),v(r.current,n);var i=r.current;let l=(0,o.useRef)(void 0);return(0,o.useEffect)(function(){if(e.pathOptions!==l.current){let n=e.pathOptions??{};i.instance.setStyle(n),l.current=n}},[i,e]),r}))}(function({positions:e,...n},t){let r=new a.Polyline(e,n);return x(r,i(t,{overlayContainer:r}))},function(e,n,t){n.positions!==t.positions&&e.setLatLngs(n.positions)});delete u().Icon.Default.prototype._getIconUrl,u().Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});let C=new(u()).Icon({iconUrl:"data:image/svg+xml;base64,"+btoa('\n    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n      <circle cx="12" cy="12" r="8" fill="#3B82F6" stroke="#FFFFFF" stroke-width="3"/>\n      <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>\n    </svg>\n  '),iconSize:[24,24],iconAnchor:[12,12],popupAnchor:[0,-12]}),I=new(u()).Icon({iconUrl:"data:image/svg+xml;base64,"+btoa('\n    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n      <circle cx="8" cy="8" r="6" fill="#10B981" stroke="#FFFFFF" stroke-width="2"/>\n      <circle cx="8" cy="8" r="2" fill="#FFFFFF"/>\n    </svg>\n  '),iconSize:[16,16],iconAnchor:[8,8],popupAnchor:[0,-8]});function L(e){let{currentLocation:n,autoCenter:t}=e,r=c().map,i=(0,o.useRef)(!1);return(0,o.useEffect)(()=>{if(n&&t){let{latitude:e,longitude:t}=n;i.current?r.panTo([e,t],{animate:!0,duration:1}):(r.setView([e,t],16),i.current=!0)}},[n,t,r]),null}function k(e){let{currentLocation:n,locationHistory:t,autoCenter:i=!0,showHistory:l=!0,showPath:c=!0,className:a=""}=e,[u,d]=(0,o.useState)(!1),f=n?[n.latitude,n.longitude]:[40.7128,-74.006],p=c&&t.length>1?t.map(e=>[e.latitude,e.longitude]):[],g=e=>{let n=e.timestamp?new Date(e.timestamp).toLocaleTimeString():"Unknown";return"\n      <div>\n        <strong>Location Update</strong><br/>\n        <strong>Time:</strong> ".concat(n,"<br/>\n        <strong>Coordinates:</strong> ").concat(e.latitude.toFixed(6),", ").concat(e.longitude.toFixed(6),"<br/>\n        ").concat(e.accuracy?"<strong>Accuracy:</strong> ".concat(e.accuracy.toFixed(0),"m<br/>"):"","\n        ").concat(e.speed?"<strong>Speed:</strong> ".concat((3.6*e.speed).toFixed(1)," km/h<br/>"):"","\n        ").concat(e.altitude?"<strong>Altitude:</strong> ".concat(e.altitude.toFixed(0),"m<br/>"):"","\n      </div>\n    ")};return(0,r.jsxs)("div",{className:"relative ".concat(a),children:[(0,r.jsxs)(s,{center:f,zoom:13,className:"w-full h-full",whenReady:()=>d(!0),children:[(0,r.jsx)(w,{attribution:'\xa9 <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),u&&(0,r.jsx)(L,{currentLocation:n,autoCenter:i}),n&&(0,r.jsx)(b,{position:[n.latitude,n.longitude],icon:C,children:(0,r.jsx)(F,{children:(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:g(n)}})})}),l&&t.slice(0,-1).map((e,n)=>(0,r.jsx)(b,{position:[e.latitude,e.longitude],icon:I,children:(0,r.jsx)(F,{children:(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:g(e)}})})},"".concat(e.latitude,"-").concat(e.longitude,"-").concat(n))),c&&p.length>1&&(0,r.jsx)(j,{positions:p,color:"#3B82F6",weight:3,opacity:.7})]}),(0,r.jsxs)("div",{className:"absolute top-4 right-4 z-[1000] space-y-2",children:[n&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-2 text-xs",children:[(0,r.jsx)("div",{className:"font-medium text-gray-800",children:"Current Location"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[n.latitude.toFixed(6),", ",n.longitude.toFixed(6)]}),n.accuracy&&(0,r.jsxs)("div",{className:"text-gray-500",children:["\xb1",n.accuracy.toFixed(0),"m"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-2 text-xs",children:[(0,r.jsx)("div",{className:"font-medium text-gray-800",children:"History"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[t.length," points"]})]})]})]})}}}]);