(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{1642:(e,t,n)=>{Promise.resolve().then(n.bind(n,4062))},4062:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var a=n(5155),c=n(2115),o=n(6750);let s={enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4,updateInterval:5e3,autoStart:!1},r=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={...s,...e},[n,a]=(0,c.useState)(null),[o,r]=(0,c.useState)(null),[i,l]=(0,c.useState)(!1),[d,u]=(0,c.useState)(!1),m=(0,c.useRef)(null),g=(0,c.useRef)(null),x="geolocation"in navigator,h=(0,c.useCallback)(e=>{a({latitude:e.coords.latitude,longitude:e.coords.longitude,accuracy:e.coords.accuracy,altitude:e.coords.altitude||void 0,altitudeAccuracy:e.coords.altitudeAccuracy||void 0,heading:e.coords.heading||void 0,speed:e.coords.speed||void 0,timestamp:new Date(e.timestamp).toISOString()}),r(null),l(!1)},[]),v=(0,c.useCallback)(e=>{let t={code:e.code,message:p(e.code),timestamp:Date.now()};r(t),l(!1),console.error("Geolocation error:",t)},[]),p=e=>{switch(e){case 1:return"Location access denied by user";case 2:return"Location information unavailable";case 3:return"Location request timeout";default:return"Unknown location error"}},b=(0,c.useCallback)(()=>new Promise((e,n)=>{if(!x)return void n(Error("Geolocation is not supported"));l(!0),r(null),navigator.geolocation.getCurrentPosition(t=>{let n={latitude:t.coords.latitude,longitude:t.coords.longitude,accuracy:t.coords.accuracy,altitude:t.coords.altitude||void 0,altitudeAccuracy:t.coords.altitudeAccuracy||void 0,heading:t.coords.heading||void 0,speed:t.coords.speed||void 0,timestamp:new Date(t.timestamp).toISOString()};a(n),l(!1),e(n)},e=>{v(e),n(e)},{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge})}),[x,t,v]),f=(0,c.useCallback)(()=>{x&&!d&&(u(!0),l(!0),r(null),m.current=navigator.geolocation.watchPosition(h,v,{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge}),t.updateInterval>0&&(g.current=setInterval(()=>{navigator.geolocation.getCurrentPosition(h,e=>{console.warn("Interval location update failed:",e)},{enableHighAccuracy:t.enableHighAccuracy,timeout:t.timeout,maximumAge:t.maximumAge})},t.updateInterval)))},[x,d,t,h,v]),j=(0,c.useCallback)(()=>{d&&(u(!1),l(!1),null!==m.current&&(navigator.geolocation.clearWatch(m.current),m.current=null),null!==g.current&&(clearInterval(g.current),g.current=null))},[d]),y=(0,c.useCallback)(()=>{r(null)},[]);return(0,c.useEffect)(()=>(t.autoStart&&x&&f(),()=>{j()}),[t.autoStart,x,f,j]),(0,c.useEffect)(()=>()=>{j()},[j]),{location:n,error:o,isLoading:i,isSupported:x,isTracking:d,startTracking:f,stopTracking:j,getCurrentLocation:b,clearError:y}};function i(){var e;let[t,n]=(0,c.useState)(!1),[s,i]=(0,c.useState)(null),[l,d]=(0,c.useState)({locationsSent:0,lastSentAt:null,startedAt:null}),{socket:u,isConnected:m,connectionCount:g,joinAsAdmin:x,sendLocation:h}=(0,o.F)(),{location:v,error:p,isLoading:b,isSupported:f,isTracking:j,startTracking:y,stopTracking:k,clearError:w}=r({enableHighAccuracy:!0,timeout:1e4,maximumAge:5e3,updateInterval:3e3}),A=(0,c.useRef)("");(0,c.useEffect)(()=>{m&&x()},[m,x]),(0,c.useEffect)(()=>{if(v&&t&&m){let e="".concat(v.latitude,"-").concat(v.longitude,"-").concat(v.timestamp);e!==A.current&&(h(v),A.current=e,d(e=>({...e,locationsSent:e.locationsSent+1,lastSentAt:new Date})))}},[v,t,m,h]);let S=async()=>{try{if("wakeLock"in navigator){let e=await navigator.wakeLock.request("screen");i(e),console.log("Wake lock acquired"),e.addEventListener("release",()=>{console.log("Wake lock released"),i(null)})}}catch(e){console.error("Failed to acquire wake lock:",e)}},N=()=>{s&&(s.release(),i(null))},C=async()=>{if(!f)return void alert("Geolocation is not supported on this device");try{n(!0),d(e=>({...e,startedAt:new Date,locationsSent:0})),await S(),y()}catch(e){console.error("Failed to start tracking:",e),n(!1)}},L=e=>e?e.toLocaleTimeString():"Never";return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-4",children:(0,a.jsx)("div",{className:"max-w-md mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"\uD83D\uDCCD Admin Panel"}),(0,a.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connection"}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(m?"bg-green-500":"bg-red-500")})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsxs)("div",{children:["Status: ",m?"Connected":"Disconnected"]}),(0,a.jsxs)("div",{children:["Trackers: ",g.trackers]}),(0,a.jsxs)("div",{children:["Total: ",g.total]})]})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Location"}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(j?"bg-green-500":b?"bg-yellow-500":"bg-gray-400")})]}),v&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsxs)("div",{children:["Lat: ",v.latitude.toFixed(6)]}),(0,a.jsxs)("div",{children:["Lng: ",v.longitude.toFixed(6)]}),(0,a.jsxs)("div",{children:["Accuracy: ",null==(e=v.accuracy)?void 0:e.toFixed(0),"m"]}),(0,a.jsxs)("div",{children:["Updated: ",L(v.timestamp?new Date(v.timestamp):null)]})]}),p&&(0,a.jsxs)("div",{className:"text-xs text-red-500 mt-2",children:["Error: ",p.message,(0,a.jsx)("button",{onClick:w,className:"ml-2 text-blue-500 underline",children:"Clear"})]})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 rounded-lg bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Statistics"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsxs)("div",{children:["Locations sent: ",l.locationsSent]}),(0,a.jsxs)("div",{children:["Last sent: ",L(l.lastSentAt)]}),(0,a.jsxs)("div",{children:["Running for: ",(e=>{if(!e)return"0s";let t=Math.floor((Date.now()-e.getTime())/1e3),n=Math.floor(t/3600),a=Math.floor(t%3600/60),c=t%60;return n>0?"".concat(n,"h ").concat(a,"m ").concat(c,"s"):a>0?"".concat(a,"m ").concat(c,"s"):"".concat(c,"s")})(l.startedAt)]}),(0,a.jsxs)("div",{children:["Wake lock: ",s?"Active":"Inactive"]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[t?(0,a.jsx)("button",{onClick:()=>{n(!1),k(),N()},className:"w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:"Stop Broadcasting"}):(0,a.jsx)("button",{onClick:C,disabled:!f||!m,className:"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:f?m?"Start Broadcasting":"Connecting...":"Location Not Supported"}),t&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"}),"Broadcasting Live"]})})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-xs text-blue-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Keep this page open and active"}),(0,a.jsx)("li",{children:"• Allow location permissions when prompted"}),(0,a.jsx)("li",{children:"• Keep your device charged"}),(0,a.jsx)("li",{children:"• Avoid switching apps frequently"})]})]})]})})})}},6750:(e,t,n)=>{"use strict";n.d(t,{F:()=>o});var a=n(2115),c=n(4298);let o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoConnect:t=!0,reconnection:n=!0,reconnectionAttempts:o=5,reconnectionDelay:s=1e3}=e,[r,i]=(0,a.useState)(null),[l,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)({total:0,admins:0,trackers:0}),g=(0,a.useRef)(),x=(0,a.useRef)(0);(0,a.useEffect)(()=>{if(!t)return;let e=(0,c.io)({reconnection:n,reconnectionAttempts:o,reconnectionDelay:s,transports:["websocket","polling"]});return e.on("connect",()=>{console.log("Socket connected:",e.id),d(!0),x.current=0}),e.on("disconnect",t=>{console.log("Socket disconnected:",t),d(!1),("io server disconnect"===t||"transport close"===t)&&h(e)}),e.on("connect_error",t=>{console.error("Socket connection error:",t),d(!1),h(e)}),e.on("connection-count",e=>{m(e)}),i(e),()=>{g.current&&clearTimeout(g.current),e.disconnect()}},[t,n,o,s]);let h=e=>{if(x.current<o){x.current++;let t=s*Math.pow(2,x.current-1);console.log("Attempting reconnection ".concat(x.current,"/").concat(o," in ").concat(t,"ms")),g.current=setTimeout(()=>{e.connected||e.connect()},t)}else console.error("Max reconnection attempts reached")};return{socket:r,isConnected:l,connectionCount:u,joinAsAdmin:()=>{r&&l&&(r.emit("join-as-admin"),console.log("Joined as admin"))},joinAsTracker:()=>{r&&l&&(r.emit("join-as-tracker"),console.log("Joined as tracker"))},sendLocation:e=>{r&&l&&r.emit("location-update",e)},disconnect:()=>{r&&(r.disconnect(),i(null),d(!1))},reconnect:()=>{r&&(x.current=0,r.connect())}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[298,441,684,358],()=>t(1642)),_N_E=e.O()}]);