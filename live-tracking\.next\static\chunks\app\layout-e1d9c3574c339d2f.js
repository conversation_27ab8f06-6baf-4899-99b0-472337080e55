(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5080:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>l});var s=r(5155),a=r(2115);class l extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by boundary:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-screen bg-red-50 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-xl p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"The application encountered an unexpected error."}),this.state.error&&(0,s.jsx)("div",{className:"bg-gray-100 rounded p-3 mb-4 text-left",children:(0,s.jsx)("div",{className:"text-sm font-mono text-gray-700",children:this.state.error.message})}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors",children:"Reload Page"})]})})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}},6566:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2093,23)),Promise.resolve().then(r.t.bind(r,7735,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,5080))},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,441,684,358],()=>t(6566)),_N_E=e.O()}]);