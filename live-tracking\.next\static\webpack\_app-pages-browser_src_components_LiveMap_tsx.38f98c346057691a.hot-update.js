"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_LiveMap_tsx",{

/***/ "(app-pages-browser)/./src/components/LiveMap.tsx":
/*!************************************!*\
  !*** ./src/components/LiveMap.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LiveMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/hooks.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/MapContainer.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/TileLayer.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Marker.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Popup.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Polyline.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/./node_modules/leaflet/dist/leaflet-src.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Fix for default markers in react-leaflet\ndelete (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon).Default.prototype._getIconUrl;\nleaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon.Default.mergeOptions({\n    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n// Custom marker icon for current location\nconst currentLocationIcon = new (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon)({\n    iconUrl: 'data:image/svg+xml;base64,' + btoa('\\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#3B82F6\" stroke=\"#FFFFFF\" stroke-width=\"3\"/>\\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#FFFFFF\"/>\\n    </svg>\\n  '),\n    iconSize: [\n        24,\n        24\n    ],\n    iconAnchor: [\n        12,\n        12\n    ],\n    popupAnchor: [\n        0,\n        -12\n    ]\n});\n// Custom marker icon for location history\nconst historyLocationIcon = new (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon)({\n    iconUrl: 'data:image/svg+xml;base64,' + btoa('\\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n      <circle cx=\"8\" cy=\"8\" r=\"6\" fill=\"#10B981\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\\n      <circle cx=\"8\" cy=\"8\" r=\"2\" fill=\"#FFFFFF\"/>\\n    </svg>\\n  '),\n    iconSize: [\n        16,\n        16\n    ],\n    iconAnchor: [\n        8,\n        8\n    ],\n    popupAnchor: [\n        0,\n        -8\n    ]\n});\n// Component to control map view\nfunction MapController(param) {\n    let { currentLocation, autoCenter } = param;\n    _s();\n    const map = (0,react_leaflet__WEBPACK_IMPORTED_MODULE_3__.useMap)();\n    const hasInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapController.useEffect\": ()=>{\n            if (currentLocation && autoCenter) {\n                const { latitude, longitude } = currentLocation;\n                if (!hasInitializedRef.current) {\n                    // Initial zoom to location\n                    map.setView([\n                        latitude,\n                        longitude\n                    ], 16);\n                    hasInitializedRef.current = true;\n                } else {\n                    // Smooth pan to new location\n                    map.panTo([\n                        latitude,\n                        longitude\n                    ], {\n                        animate: true,\n                        duration: 1\n                    });\n                }\n            }\n        }\n    }[\"MapController.useEffect\"], [\n        currentLocation,\n        autoCenter,\n        map\n    ]);\n    return null;\n}\n_s(MapController, \"HE4H5PO7vM7VkVDZWhtoA7gfS3k=\", false, function() {\n    return [\n        react_leaflet__WEBPACK_IMPORTED_MODULE_3__.useMap\n    ];\n});\n_c = MapController;\nfunction LiveMap(param) {\n    let { currentLocation, locationHistory, autoCenter = true, showHistory = true, showPath = true, className = '' } = param;\n    _s1();\n    const [mapReady, setMapReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Default center (you can change this to your preferred default location)\n    const defaultCenter = [\n        40.7128,\n        -74.0060\n    ]; // New York City\n    const defaultZoom = 13;\n    // Calculate map center\n    const mapCenter = currentLocation ? [\n        currentLocation.latitude,\n        currentLocation.longitude\n    ] : defaultCenter;\n    // Create path from location history\n    const pathCoordinates = showPath && locationHistory.length > 1 ? locationHistory.map((loc)=>[\n            loc.latitude,\n            loc.longitude\n        ]) : [];\n    const formatLocationInfo = (location)=>{\n        const time = location.timestamp ? new Date(location.timestamp).toLocaleTimeString() : 'Unknown';\n        return \"\\n      <div>\\n        <strong>Location Update</strong><br/>\\n        <strong>Time:</strong> \".concat(time, \"<br/>\\n        <strong>Coordinates:</strong> \").concat(location.latitude.toFixed(6), \", \").concat(location.longitude.toFixed(6), \"<br/>\\n        \").concat(location.accuracy ? \"<strong>Accuracy:</strong> \".concat(location.accuracy.toFixed(0), \"m<br/>\") : '', \"\\n        \").concat(location.speed ? \"<strong>Speed:</strong> \".concat((location.speed * 3.6).toFixed(1), \" km/h<br/>\") : '', \"\\n        \").concat(location.altitude ? \"<strong>Altitude:</strong> \".concat(location.altitude.toFixed(0), \"m<br/>\") : '', \"\\n      </div>\\n    \");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_4__.MapContainer, {\n                center: mapCenter,\n                zoom: defaultZoom,\n                className: \"w-full h-full\",\n                whenReady: ()=>setMapReady(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_5__.TileLayer, {\n                        attribution: '\\xa9 <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors',\n                        url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    mapReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MapController, {\n                        currentLocation: currentLocation,\n                        autoCenter: autoCenter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_6__.Marker, {\n                        position: [\n                            currentLocation.latitude,\n                            currentLocation.longitude\n                        ],\n                        icon: currentLocationIcon,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_7__.Popup, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: formatLocationInfo(currentLocation)\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    showHistory && locationHistory.slice(0, -1).map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_6__.Marker, {\n                            position: [\n                                location.latitude,\n                                location.longitude\n                            ],\n                            icon: historyLocationIcon,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_7__.Popup, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    dangerouslySetInnerHTML: {\n                                        __html: formatLocationInfo(location)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, \"\".concat(location.latitude, \"-\").concat(location.longitude, \"-\").concat(index), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)),\n                    showPath && pathCoordinates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_8__.Polyline, {\n                        positions: pathCoordinates,\n                        color: \"#3B82F6\",\n                        weight: 3,\n                        opacity: 0.7\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-[1000] space-y-2\",\n                children: [\n                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-gray-800\",\n                                children: \"Current Location\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    currentLocation.latitude.toFixed(6),\n                                    \", \",\n                                    currentLocation.longitude.toFixed(6)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            currentLocation.accuracy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"\\xb1\",\n                                    currentLocation.accuracy.toFixed(0),\n                                    \"m\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-gray-800\",\n                                children: \"History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    locationHistory.length,\n                                    \" points\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s1(LiveMap, \"+AAQX+/540pjugCDelNFpNYdZPg=\");\n_c1 = LiveMap;\nvar _c, _c1;\n$RefreshReg$(_c, \"MapController\");\n$RefreshReg$(_c1, \"LiveMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LiveMap.tsx\n"));

/***/ })

});