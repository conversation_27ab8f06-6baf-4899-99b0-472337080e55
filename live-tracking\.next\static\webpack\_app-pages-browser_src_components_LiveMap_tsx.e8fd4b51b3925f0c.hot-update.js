"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_LiveMap_tsx",{

/***/ "(app-pages-browser)/./src/components/LiveMap.tsx":
/*!************************************!*\
  !*** ./src/components/LiveMap.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LiveMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/hooks.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/MapContainer.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/TileLayer.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Marker.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Popup.js\");\n/* harmony import */ var react_leaflet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-leaflet */ \"(app-pages-browser)/./node_modules/react-leaflet/lib/Polyline.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/./node_modules/leaflet/dist/leaflet-src.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Fix for default markers in react-leaflet\ndelete (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon).Default.prototype._getIconUrl;\nleaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon.Default.mergeOptions({\n    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\n// Custom marker icon for current location\nconst currentLocationIcon = new (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon)({\n    iconUrl: 'data:image/svg+xml;base64,' + btoa('\\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#3B82F6\" stroke=\"#FFFFFF\" stroke-width=\"3\"/>\\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#FFFFFF\"/>\\n    </svg>\\n  '),\n    iconSize: [\n        24,\n        24\n    ],\n    iconAnchor: [\n        12,\n        12\n    ],\n    popupAnchor: [\n        0,\n        -12\n    ]\n});\n// Custom marker icon for location history\nconst historyLocationIcon = new (leaflet__WEBPACK_IMPORTED_MODULE_2___default().Icon)({\n    iconUrl: 'data:image/svg+xml;base64,' + btoa('\\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n      <circle cx=\"8\" cy=\"8\" r=\"6\" fill=\"#10B981\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\\n      <circle cx=\"8\" cy=\"8\" r=\"2\" fill=\"#FFFFFF\"/>\\n    </svg>\\n  '),\n    iconSize: [\n        16,\n        16\n    ],\n    iconAnchor: [\n        8,\n        8\n    ],\n    popupAnchor: [\n        0,\n        -8\n    ]\n});\n// Component to control map view\nfunction MapController(param) {\n    let { currentLocation, autoCenter } = param;\n    _s();\n    const map = (0,react_leaflet__WEBPACK_IMPORTED_MODULE_3__.useMap)();\n    const hasInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapController.useEffect\": ()=>{\n            if (currentLocation && autoCenter) {\n                const { latitude, longitude } = currentLocation;\n                if (!hasInitializedRef.current) {\n                    // Initial zoom to location\n                    map.setView([\n                        latitude,\n                        longitude\n                    ], 16);\n                    hasInitializedRef.current = true;\n                } else {\n                    // Smooth pan to new location\n                    map.panTo([\n                        latitude,\n                        longitude\n                    ], {\n                        animate: true,\n                        duration: 1\n                    });\n                }\n            }\n        }\n    }[\"MapController.useEffect\"], [\n        currentLocation,\n        autoCenter,\n        map\n    ]);\n    return null;\n}\n_s(MapController, \"HE4H5PO7vM7VkVDZWhtoA7gfS3k=\", false, function() {\n    return [\n        react_leaflet__WEBPACK_IMPORTED_MODULE_3__.useMap\n    ];\n});\n_c = MapController;\nfunction LiveMap(param) {\n    let { currentLocation, locationHistory, autoCenter = true, showHistory = true, showPath = true, className = '' } = param;\n    _s1();\n    const [mapReady, setMapReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Default center (you can change this to your preferred default location)\n    const defaultCenter = [\n        40.7128,\n        -74.0060\n    ]; // New York City\n    const defaultZoom = 13;\n    // Calculate map center\n    const mapCenter = currentLocation ? [\n        currentLocation.latitude,\n        currentLocation.longitude\n    ] : defaultCenter;\n    // Create path from location history\n    const pathCoordinates = showPath && locationHistory.length > 1 ? locationHistory.map((loc)=>[\n            loc.latitude,\n            loc.longitude\n        ]) : [];\n    const formatLocationInfo = (location)=>{\n        const time = location.timestamp ? new Date(location.timestamp).toLocaleTimeString() : 'Unknown';\n        var _location_latitude;\n        const lat = (_location_latitude = location.latitude) !== null && _location_latitude !== void 0 ? _location_latitude : 0;\n        var _location_longitude;\n        const lng = (_location_longitude = location.longitude) !== null && _location_longitude !== void 0 ? _location_longitude : 0;\n        var _location_accuracy;\n        const acc = (_location_accuracy = location.accuracy) !== null && _location_accuracy !== void 0 ? _location_accuracy : 0;\n        return \"\\n      <div>\\n        <strong>Location Update</strong><br/>\\n        <strong>Time:</strong> \".concat(time, \"<br/>\\n        <strong>Coordinates:</strong> \").concat(lat.toFixed(6), \", \").concat(lng.toFixed(6), \"<br/>\\n        \").concat(acc > 0 ? \"<strong>Accuracy:</strong> \".concat(acc.toFixed(0), \"m<br/>\") : '', \"\\n        \").concat(location.speed ? \"<strong>Speed:</strong> \".concat((location.speed * 3.6).toFixed(1), \" km/h<br/>\") : '', \"\\n        \").concat(location.altitude ? \"<strong>Altitude:</strong> \".concat(location.altitude.toFixed(0), \"m<br/>\") : '', \"\\n      </div>\\n    \");\n    };\n    var _currentLocation_latitude, _currentLocation_longitude;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_4__.MapContainer, {\n                center: mapCenter,\n                zoom: defaultZoom,\n                className: \"w-full h-full\",\n                whenReady: ()=>setMapReady(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_5__.TileLayer, {\n                        attribution: '\\xa9 <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors',\n                        url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    mapReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MapController, {\n                        currentLocation: currentLocation,\n                        autoCenter: autoCenter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_6__.Marker, {\n                        position: [\n                            currentLocation.latitude,\n                            currentLocation.longitude\n                        ],\n                        icon: currentLocationIcon,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_7__.Popup, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: formatLocationInfo(currentLocation)\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    showHistory && locationHistory.slice(0, -1).map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_6__.Marker, {\n                            position: [\n                                location.latitude,\n                                location.longitude\n                            ],\n                            icon: historyLocationIcon,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_7__.Popup, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    dangerouslySetInnerHTML: {\n                                        __html: formatLocationInfo(location)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, \"\".concat(location.latitude, \"-\").concat(location.longitude, \"-\").concat(index), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)),\n                    showPath && pathCoordinates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_leaflet__WEBPACK_IMPORTED_MODULE_8__.Polyline, {\n                        positions: pathCoordinates,\n                        color: \"#3B82F6\",\n                        weight: 3,\n                        opacity: 0.7\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-[1000] space-y-2\",\n                children: [\n                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-gray-800\",\n                                children: \"Current Location\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    ((_currentLocation_latitude = currentLocation.latitude) !== null && _currentLocation_latitude !== void 0 ? _currentLocation_latitude : 0).toFixed(6),\n                                    \", \",\n                                    ((_currentLocation_longitude = currentLocation.longitude) !== null && _currentLocation_longitude !== void 0 ? _currentLocation_longitude : 0).toFixed(6)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this),\n                            currentLocation.accuracy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"\\xb1\",\n                                    currentLocation.accuracy.toFixed(0),\n                                    \"m\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-2 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-gray-800\",\n                                children: \"History\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    locationHistory.length,\n                                    \" points\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\components\\\\LiveMap.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s1(LiveMap, \"+AAQX+/540pjugCDelNFpNYdZPg=\");\n_c1 = LiveMap;\nvar _c, _c1;\n$RefreshReg$(_c, \"MapController\");\n$RefreshReg$(_c1, \"LiveMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LiveMap.tsx\n"));

/***/ })

});