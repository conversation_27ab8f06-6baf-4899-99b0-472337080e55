"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSupabaseLocation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSupabaseLocation */ \"(app-pages-browser)/./src/hooks/useSupabaseLocation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    var _currentLocation_accuracy;\n    _s();\n    const [wakeLock, setWakeLock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deviceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AdminPage.useState\": ()=>\"admin-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9))\n    }[\"AdminPage.useState\"]);\n    const { currentLocation, locationHistory, isConnected, isTracking, error, startTracking, stopTracking, lastUpdateTime, updateCount } = (0,_hooks_useSupabaseLocation__WEBPACK_IMPORTED_MODULE_2__.useSupabaseLocation)({\n        deviceId,\n        enableTracking: false,\n        trackingInterval: 3000,\n        enableRealtime: true\n    });\n    // Check geolocation support\n    const isSupported = typeof navigator !== 'undefined' && 'geolocation' in navigator;\n    // Request wake lock to prevent screen from sleeping\n    const requestWakeLock = async ()=>{\n        try {\n            if ('wakeLock' in navigator) {\n                const lock = await navigator.wakeLock.request('screen');\n                setWakeLock(lock);\n                console.log('Wake lock acquired');\n                lock.addEventListener('release', ()=>{\n                    console.log('Wake lock released');\n                    setWakeLock(null);\n                });\n            }\n        } catch (err) {\n            console.error('Failed to acquire wake lock:', err);\n        }\n    };\n    // Release wake lock\n    const releaseWakeLock = ()=>{\n        if (wakeLock) {\n            wakeLock.release();\n            setWakeLock(null);\n        }\n    };\n    const handleStart = async ()=>{\n        if (!isSupported) {\n            alert('Geolocation is not supported on this device');\n            return;\n        }\n        try {\n            await requestWakeLock();\n            startTracking();\n        } catch (err) {\n            console.error('Failed to start tracking:', err);\n        }\n    };\n    const handleStop = ()=>{\n        stopTracking();\n        releaseWakeLock();\n    };\n    const formatTime = (date)=>{\n        if (!date) return 'Never';\n        return date.toLocaleTimeString();\n    };\n    const getTrackingDuration = ()=>{\n        if (!isTracking || !lastUpdateTime) return '0s';\n        const duration = Math.floor((Date.now() - lastUpdateTime.getTime()) / 1000);\n        const hours = Math.floor(duration / 3600);\n        const minutes = Math.floor(duration % 3600 / 60);\n        const seconds = duration % 60;\n        if (hours > 0) return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        if (minutes > 0) return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        return \"\".concat(seconds, \"s\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-center mb-6 text-gray-800\",\n                        children: \"\\uD83D\\uDCCD Admin Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Supabase Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Status: \",\n                                            isConnected ? 'Connected' : 'Disconnected'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Device ID: \",\n                                            deviceId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Updates sent: \",\n                                            updateCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(isTracking ? 'bg-green-500' : isConnected ? 'bg-blue-500' : 'bg-gray-400')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Lat: \",\n                                            currentLocation.latitude.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Lng: \",\n                                            currentLocation.longitude.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Accuracy: \",\n                                            (_currentLocation_accuracy = currentLocation.accuracy) === null || _currentLocation_accuracy === void 0 ? void 0 : _currentLocation_accuracy.toFixed(0),\n                                            \"m\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Updated: \",\n                                            formatTime(currentLocation.timestamp ? new Date(currentLocation.timestamp) : null)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-500 mt-2\",\n                                children: [\n                                    \"Error: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"Statistics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Locations sent: \",\n                                            updateCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Last sent: \",\n                                            formatTime(lastUpdateTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"History count: \",\n                                            locationHistory.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            \"Wake lock: \",\n                                            wakeLock ? 'Active' : 'Inactive'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            !isTracking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStart,\n                                disabled: !isSupported,\n                                className: \"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors\",\n                                children: !isSupported ? 'Location Not Supported' : 'Start Broadcasting'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStop,\n                                className: \"w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors\",\n                                children: \"Stop Broadcasting\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            isTracking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Broadcasting to Supabase\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"Instructions:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-blue-600 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Keep this page open and active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Allow location permissions when prompted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Keep your device charged\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Avoid switching apps frequently\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"haVoczXG4lqznXWfATdeovzxoao=\", false, function() {\n    return [\n        _hooks_useSupabaseLocation__WEBPACK_IMPORTED_MODULE_2__.useSupabaseLocation\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});