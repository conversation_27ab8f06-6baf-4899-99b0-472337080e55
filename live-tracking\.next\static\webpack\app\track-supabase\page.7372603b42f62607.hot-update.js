"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/track-supabase/page",{

/***/ "(app-pages-browser)/./src/app/track-supabase/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/track-supabase/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrackSupabasePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import the map component to avoid SSR issues\nconst LiveMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_LiveMap_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/LiveMap */ \"(app-pages-browser)/./src/components/LiveMap.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\track-supabase\\\\page.tsx -> \" + \"@/components/LiveMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading map...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 5\n        }, undefined)\n});\n_c = LiveMap;\nfunction TrackSupabasePage() {\n    _s();\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [locationHistory, setLocationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeDevices, setActiveDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoCenter, setAutoCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPath, setShowPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdateTime, setLastUpdateTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrackSupabasePage.useEffect\": ()=>{\n            const loadInitialData = {\n                \"TrackSupabasePage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setError(null);\n                        // Get all active devices\n                        const devices = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getActiveDevices(60); // Last 60 minutes\n                        setActiveDevices(devices);\n                        // If we have devices, select the first one and load its data\n                        if (devices.length > 0 && !selectedDevice) {\n                            const firstDevice = devices[0];\n                            setSelectedDevice(firstDevice.device_id);\n                            // Set current location\n                            setCurrentLocation({\n                                lat: firstDevice.latitude,\n                                lng: firstDevice.longitude,\n                                accuracy: firstDevice.accuracy,\n                                timestamp: firstDevice.timestamp\n                            });\n                            // Load history for this device\n                            const history = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLocationHistory(firstDevice.device_id, 100);\n                            setLocationHistory(history);\n                        }\n                        setIsConnected(true);\n                    } catch (err) {\n                        console.error('Error loading initial data:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                        setIsConnected(false);\n                    }\n                }\n            }[\"TrackSupabasePage.useEffect.loadInitialData\"];\n            loadInitialData();\n        }\n    }[\"TrackSupabasePage.useEffect\"], [\n        selectedDevice\n    ]);\n    // Set up real-time subscription\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrackSupabasePage.useEffect\": ()=>{\n            let subscription = null;\n            const setupRealtimeSubscription = {\n                \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": ()=>{\n                    subscription = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.subscribeToLocationUpdates({\n                        \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (payload)=>{\n                            console.log('Real-time update received:', payload);\n                            if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {\n                                const newLocation = payload.new;\n                                // Update current location if it's from the selected device or if no device is selected\n                                if (!selectedDevice || newLocation.device_id === selectedDevice) {\n                                    setCurrentLocation({\n                                        lat: newLocation.latitude,\n                                        lng: newLocation.longitude,\n                                        accuracy: newLocation.accuracy,\n                                        timestamp: newLocation.timestamp\n                                    });\n                                    setLastUpdateTime(new Date());\n                                    // Add to history\n                                    setLocationHistory({\n                                        \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (prev)=>[\n                                                newLocation,\n                                                ...prev.slice(0, 99)\n                                            ]\n                                    }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                                }\n                                // Update active devices list\n                                setActiveDevices({\n                                    \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (prev)=>{\n                                        const existingIndex = prev.findIndex({\n                                            \"TrackSupabasePage.useEffect.setupRealtimeSubscription.existingIndex\": (d)=>d.device_id === newLocation.device_id\n                                        }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription.existingIndex\"]);\n                                        if (existingIndex >= 0) {\n                                            const updated = [\n                                                ...prev\n                                            ];\n                                            updated[existingIndex] = newLocation;\n                                            return updated;\n                                        } else {\n                                            return [\n                                                newLocation,\n                                                ...prev\n                                            ];\n                                        }\n                                    }\n                                }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                                setIsConnected(true);\n                            }\n                        }\n                    }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                }\n            }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"];\n            setupRealtimeSubscription();\n            return ({\n                \"TrackSupabasePage.useEffect\": ()=>{\n                    if (subscription) {\n                        subscription.unsubscribe();\n                    }\n                }\n            })[\"TrackSupabasePage.useEffect\"];\n        }\n    }[\"TrackSupabasePage.useEffect\"], [\n        selectedDevice\n    ]);\n    // Handle device selection change\n    const handleDeviceChange = async (deviceId)=>{\n        if (deviceId === selectedDevice) return;\n        setSelectedDevice(deviceId);\n        try {\n            setError(null);\n            // Load data for the selected device\n            const latestLocation = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLatestLocation(deviceId);\n            if (latestLocation) {\n                setCurrentLocation({\n                    lat: latestLocation.latitude,\n                    lng: latestLocation.longitude,\n                    accuracy: latestLocation.accuracy,\n                    timestamp: latestLocation.timestamp\n                });\n            }\n            // Load history\n            const history = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLocationHistory(deviceId, 100);\n            setLocationHistory(history);\n        } catch (err) {\n            console.error('Error loading device data:', err);\n            setError(err instanceof Error ? err.message : 'Failed to load device data');\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return 'Never';\n        return date.toLocaleTimeString();\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString();\n    };\n    // Convert location history to the format expected by LiveMap\n    const mapLocations = locationHistory.map((loc)=>({\n            latitude: loc.latitude,\n            longitude: loc.longitude,\n            accuracy: loc.accuracy,\n            timestamp: loc.timestamp\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-800\",\n                                children: \"\\uD83D\\uDCCD Live Tracking (Supabase)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    activeDevices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedDevice,\n                                        onChange: (e)=>handleDeviceChange(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded-md text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Device\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            activeDevices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: device.device_id,\n                                                    children: [\n                                                        device.device_id.substring(0, 20),\n                                                        \"...\"\n                                                    ]\n                                                }, device.device_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: isConnected ? 'Connected' : 'Disconnected'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center justify-between text-xs text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Active Devices: \",\n                                            activeDevices.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"History: \",\n                                            locationHistory.length,\n                                            \" points\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Last Update: \",\n                                            formatTimestamp(currentLocation.timestamp)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500\",\n                                children: [\n                                    \"Error: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: autoCenter,\n                                    onChange: (e)=>setAutoCenter(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Auto Center\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: showHistory,\n                                    onChange: (e)=>setShowHistory(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Show History\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: showPath,\n                                    onChange: (e)=>setShowPath(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Show Path\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: currentLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveMap, {\n                    currentLocation: currentLocation,\n                    locationHistory: showHistory ? mapLocations : [],\n                    autoCenter: autoCenter,\n                    showPath: showPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500 mb-4\",\n                            children: activeDevices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg mb-2\",\n                                        children: \"\\uD83D\\uDCF1 No Active Devices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Start broadcasting from an admin device to see location updates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg mb-2\",\n                                        children: \"\\uD83D\\uDCCD Select a Device\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Choose a device from the dropdown to start tracking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Latitude\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: currentLocation.lat.toFixed(6)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Longitude\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: currentLocation.lng.toFixed(6)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Accuracy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: [\n                                        currentLocation.accuracy.toFixed(0),\n                                        \"m\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: formatTimestamp(currentLocation.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(TrackSupabasePage, \"hhtiX3x9SpT8hUtsA3Mzd4Ib8iU=\");\n_c1 = TrackSupabasePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"LiveMap\");\n$RefreshReg$(_c1, \"TrackSupabasePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdHJhY2stc3VwYWJhc2UvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFNEM7QUFDVDtBQUNtQztBQUd0RSwyREFBMkQ7QUFDM0QsTUFBTUksVUFBVUYsd0RBQU9BLENBQUMsSUFBTSwwTkFBOEI7Ozs7OztJQUMxREcsS0FBSztJQUNMQyxTQUFTLGtCQUNQLDhEQUFDQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0tBTmpDSjtBQW1CUyxTQUFTSzs7SUFDdEIsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHViwrQ0FBUUEsQ0FBMEI7SUFDaEYsTUFBTSxDQUFDVyxpQkFBaUJDLG1CQUFtQixHQUFHWiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUN6RSxNQUFNLENBQUNhLGVBQWVDLGlCQUFpQixHQUFHZCwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNlLFlBQVlDLGNBQWMsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2lCLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21CLFVBQVVDLFlBQVksR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3FCLGdCQUFnQkMsa0JBQWtCLEdBQUd0QiwrQ0FBUUEsQ0FBYztJQUNsRSxNQUFNLENBQUN1QixhQUFhQyxlQUFlLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN5QixPQUFPQyxTQUFTLEdBQUcxQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDMkIsZ0JBQWdCQyxrQkFBa0IsR0FBRzVCLCtDQUFRQSxDQUFTO0lBRTdELG9CQUFvQjtJQUNwQkQsZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTThCOytEQUFrQjtvQkFDdEIsSUFBSTt3QkFDRkgsU0FBUzt3QkFFVCx5QkFBeUI7d0JBQ3pCLE1BQU1JLFVBQVUsTUFBTTVCLDBEQUFlQSxDQUFDNkIsZ0JBQWdCLENBQUMsS0FBSyxrQkFBa0I7d0JBQzlFakIsaUJBQWlCZ0I7d0JBRWpCLDZEQUE2RDt3QkFDN0QsSUFBSUEsUUFBUUUsTUFBTSxHQUFHLEtBQUssQ0FBQ0wsZ0JBQWdCOzRCQUN6QyxNQUFNTSxjQUFjSCxPQUFPLENBQUMsRUFBRTs0QkFDOUJGLGtCQUFrQkssWUFBWUMsU0FBUzs0QkFFdkMsdUJBQXVCOzRCQUN2QnhCLG1CQUFtQjtnQ0FDakJ5QixLQUFLRixZQUFZRyxRQUFRO2dDQUN6QkMsS0FBS0osWUFBWUssU0FBUztnQ0FDMUJDLFVBQVVOLFlBQVlNLFFBQVE7Z0NBQzlCQyxXQUFXUCxZQUFZTyxTQUFTOzRCQUNsQzs0QkFFQSwrQkFBK0I7NEJBQy9CLE1BQU1DLFVBQVUsTUFBTXZDLDBEQUFlQSxDQUFDd0Msa0JBQWtCLENBQUNULFlBQVlDLFNBQVMsRUFBRTs0QkFDaEZ0QixtQkFBbUI2Qjt3QkFDckI7d0JBRUFqQixlQUFlO29CQUNqQixFQUFFLE9BQU9tQixLQUFLO3dCQUNaQyxRQUFRbkIsS0FBSyxDQUFDLCtCQUErQmtCO3dCQUM3Q2pCLFNBQVNpQixlQUFlRSxRQUFRRixJQUFJRyxPQUFPLEdBQUc7d0JBQzlDdEIsZUFBZTtvQkFDakI7Z0JBQ0Y7O1lBRUFLO1FBQ0Y7c0NBQUc7UUFBQ0Y7S0FBZTtJQUVuQixnQ0FBZ0M7SUFDaEM1QixnREFBU0E7dUNBQUM7WUFDUixJQUFJZ0QsZUFBdUM7WUFFM0MsTUFBTUM7eUVBQTRCO29CQUNoQ0QsZUFBZTdDLDBEQUFlQSxDQUFDK0MsMEJBQTBCO2lGQUFDLENBQUNDOzRCQUN6RE4sUUFBUU8sR0FBRyxDQUFDLDhCQUE4QkQ7NEJBRTFDLElBQUlBLFFBQVFFLFNBQVMsS0FBSyxZQUFZRixRQUFRRSxTQUFTLEtBQUssVUFBVTtnQ0FDcEUsTUFBTUMsY0FBY0gsUUFBUUksR0FBRztnQ0FFL0IsdUZBQXVGO2dDQUN2RixJQUFJLENBQUMzQixrQkFBa0IwQixZQUFZbkIsU0FBUyxLQUFLUCxnQkFBZ0I7b0NBQy9EakIsbUJBQW1CO3dDQUNqQnlCLEtBQUtrQixZQUFZakIsUUFBUTt3Q0FDekJDLEtBQUtnQixZQUFZZixTQUFTO3dDQUMxQkMsVUFBVWMsWUFBWWQsUUFBUTt3Q0FDOUJDLFdBQVdhLFlBQVliLFNBQVM7b0NBQ2xDO29DQUVBbEIsa0JBQWtCLElBQUlpQztvQ0FFdEIsaUJBQWlCO29DQUNqQjNDO2lHQUFtQjRDLENBQUFBLE9BQVE7Z0RBQUNIO21EQUFnQkcsS0FBS0MsS0FBSyxDQUFDLEdBQUc7NkNBQUk7O2dDQUNoRTtnQ0FFQSw2QkFBNkI7Z0NBQzdCM0M7NkZBQWlCMEMsQ0FBQUE7d0NBQ2YsTUFBTUUsZ0JBQWdCRixLQUFLRyxTQUFTO21IQUFDQyxDQUFBQSxJQUFLQSxFQUFFMUIsU0FBUyxLQUFLbUIsWUFBWW5CLFNBQVM7O3dDQUMvRSxJQUFJd0IsaUJBQWlCLEdBQUc7NENBQ3RCLE1BQU1HLFVBQVU7bURBQUlMOzZDQUFLOzRDQUN6QkssT0FBTyxDQUFDSCxjQUFjLEdBQUdMOzRDQUN6QixPQUFPUTt3Q0FDVCxPQUFPOzRDQUNMLE9BQU87Z0RBQUNSO21EQUFnQkc7NkNBQUs7d0NBQy9CO29DQUNGOztnQ0FFQWhDLGVBQWU7NEJBQ2pCO3dCQUNGOztnQkFDRjs7WUFFQXdCO1lBRUE7K0NBQU87b0JBQ0wsSUFBSUQsY0FBYzt3QkFDaEJBLGFBQWFlLFdBQVc7b0JBQzFCO2dCQUNGOztRQUNGO3NDQUFHO1FBQUNuQztLQUFlO0lBRW5CLGlDQUFpQztJQUNqQyxNQUFNb0MscUJBQXFCLE9BQU9DO1FBQ2hDLElBQUlBLGFBQWFyQyxnQkFBZ0I7UUFFakNDLGtCQUFrQm9DO1FBRWxCLElBQUk7WUFDRnRDLFNBQVM7WUFFVCxvQ0FBb0M7WUFDcEMsTUFBTXVDLGlCQUFpQixNQUFNL0QsMERBQWVBLENBQUNnRSxpQkFBaUIsQ0FBQ0Y7WUFDL0QsSUFBSUMsZ0JBQWdCO2dCQUNsQnZELG1CQUFtQjtvQkFDakJ5QixLQUFLOEIsZUFBZTdCLFFBQVE7b0JBQzVCQyxLQUFLNEIsZUFBZTNCLFNBQVM7b0JBQzdCQyxVQUFVMEIsZUFBZTFCLFFBQVE7b0JBQ2pDQyxXQUFXeUIsZUFBZXpCLFNBQVM7Z0JBQ3JDO1lBQ0Y7WUFFQSxlQUFlO1lBQ2YsTUFBTUMsVUFBVSxNQUFNdkMsMERBQWVBLENBQUN3QyxrQkFBa0IsQ0FBQ3NCLFVBQVU7WUFDbkVwRCxtQkFBbUI2QjtRQUVyQixFQUFFLE9BQU9FLEtBQUs7WUFDWkMsUUFBUW5CLEtBQUssQ0FBQyw4QkFBOEJrQjtZQUM1Q2pCLFNBQVNpQixlQUFlRSxRQUFRRixJQUFJRyxPQUFPLEdBQUc7UUFDaEQ7SUFDRjtJQUVBLE1BQU1xQixhQUFhLENBQUNDO1FBQ2xCLElBQUksQ0FBQ0EsTUFBTSxPQUFPO1FBQ2xCLE9BQU9BLEtBQUtDLGtCQUFrQjtJQUNoQztJQUVBLE1BQU1DLGtCQUFrQixDQUFDOUI7UUFDdkIsT0FBTyxJQUFJZSxLQUFLZixXQUFXNkIsa0JBQWtCO0lBQy9DO0lBRUEsNkRBQTZEO0lBQzdELE1BQU1FLGVBQWU1RCxnQkFBZ0I2RCxHQUFHLENBQUNDLENBQUFBLE1BQVE7WUFDL0NyQyxVQUFVcUMsSUFBSXJDLFFBQVE7WUFDdEJFLFdBQVdtQyxJQUFJbkMsU0FBUztZQUN4QkMsVUFBVWtDLElBQUlsQyxRQUFRO1lBQ3RCQyxXQUFXaUMsSUFBSWpDLFNBQVM7UUFDMUI7SUFFQSxxQkFDRSw4REFBQ2xDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ21FO2dDQUFHbkUsV0FBVTswQ0FBa0M7Ozs7OzswQ0FFaEQsOERBQUNEO2dDQUFJQyxXQUFVOztvQ0FFWk0sY0FBY21CLE1BQU0sR0FBRyxtQkFDdEIsOERBQUMyQzt3Q0FDQ0MsT0FBT2pEO3dDQUNQa0QsVUFBVSxDQUFDQyxJQUFNZixtQkFBbUJlLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDbERyRSxXQUFVOzswREFFViw4REFBQ3lFO2dEQUFPSixPQUFNOzBEQUFHOzs7Ozs7NENBQ2hCL0QsY0FBYzJELEdBQUcsQ0FBQ1MsQ0FBQUEsdUJBQ2pCLDhEQUFDRDtvREFBOEJKLE9BQU9LLE9BQU8vQyxTQUFTOzt3REFDbkQrQyxPQUFPL0MsU0FBUyxDQUFDZ0QsU0FBUyxDQUFDLEdBQUc7d0RBQUk7O21EQUR4QkQsT0FBTy9DLFNBQVM7Ozs7Ozs7Ozs7O2tEQVFuQyw4REFBQzVCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVcsd0JBQW9FLE9BQTVDZ0IsY0FBYyxpQkFBaUI7Ozs7OzswREFDdkUsOERBQUM0RDtnREFBSzVFLFdBQVU7MERBQ2JnQixjQUFjLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPckMsOERBQUNqQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzRFOzs0Q0FBSzs0Q0FBaUJ0RSxjQUFjbUIsTUFBTTs7Ozs7OztrREFDM0MsOERBQUNtRDs7NENBQUs7NENBQVV4RSxnQkFBZ0JxQixNQUFNOzRDQUFDOzs7Ozs7O29DQUN0Q3ZCLGlDQUNDLDhEQUFDMEU7OzRDQUFLOzRDQUNVYixnQkFBZ0I3RCxnQkFBZ0IrQixTQUFTOzs7Ozs7Ozs7Ozs7OzRCQUs1RGYsdUJBQ0MsOERBQUNuQjtnQ0FBSUMsV0FBVTs7b0NBQWU7b0NBQ3BCa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2hCLDhEQUFDbkI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzZFOzRCQUFNN0UsV0FBVTs7OENBQ2YsOERBQUM4RTtvQ0FDQ0MsTUFBSztvQ0FDTEMsU0FBU3hFO29DQUNUOEQsVUFBVSxDQUFDQyxJQUFNOUQsY0FBYzhELEVBQUVDLE1BQU0sQ0FBQ1EsT0FBTztvQ0FDL0NoRixXQUFVOzs7Ozs7OENBRVosOERBQUM0RTtvQ0FBSzVFLFdBQVU7OENBQVU7Ozs7Ozs7Ozs7OztzQ0FHNUIsOERBQUM2RTs0QkFBTTdFLFdBQVU7OzhDQUNmLDhEQUFDOEU7b0NBQ0NDLE1BQUs7b0NBQ0xDLFNBQVN0RTtvQ0FDVDRELFVBQVUsQ0FBQ0MsSUFBTTVELGVBQWU0RCxFQUFFQyxNQUFNLENBQUNRLE9BQU87b0NBQ2hEaEYsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDNEU7b0NBQUs1RSxXQUFVOzhDQUFVOzs7Ozs7Ozs7Ozs7c0NBRzVCLDhEQUFDNkU7NEJBQU03RSxXQUFVOzs4Q0FDZiw4REFBQzhFO29DQUNDQyxNQUFLO29DQUNMQyxTQUFTcEU7b0NBQ1QwRCxVQUFVLENBQUNDLElBQU0xRCxZQUFZMEQsRUFBRUMsTUFBTSxDQUFDUSxPQUFPO29DQUM3Q2hGLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQzRFO29DQUFLNUUsV0FBVTs4Q0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWhDLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWkUsZ0NBQ0MsOERBQUNOO29CQUNDTSxpQkFBaUJBO29CQUNqQkUsaUJBQWlCTSxjQUFjc0QsZUFBZSxFQUFFO29CQUNoRHhELFlBQVlBO29CQUNaSSxVQUFVQTs7Ozs7eUNBR1osOERBQUNiO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pNLGNBQWNtQixNQUFNLEtBQUssa0JBQ3hCOztrREFDRSw4REFBQzFCO3dDQUFJQyxXQUFVO2tEQUFlOzs7Ozs7a0RBQzlCLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBVTs7Ozs7Ozs2REFHM0I7O2tEQUNFLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBZTs7Ozs7O2tEQUM5Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVdENFLGlDQUNDLDhEQUFDSDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNBO29DQUFJQyxXQUFVOzhDQUFnQjs7Ozs7OzhDQUMvQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWFFLGdCQUFnQjBCLEdBQUcsQ0FBQ3FELE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7O3NDQUUxRCw4REFBQ2xGOzs4Q0FDQyw4REFBQ0E7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBYUUsZ0JBQWdCNEIsR0FBRyxDQUFDbUQsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7c0NBRTFELDhEQUFDbEY7OzhDQUNDLDhEQUFDQTtvQ0FBSUMsV0FBVTs4Q0FBZ0I7Ozs7Ozs4Q0FDL0IsOERBQUNEO29DQUFJQyxXQUFVOzt3Q0FBYUUsZ0JBQWdCOEIsUUFBUSxDQUFDaUQsT0FBTyxDQUFDO3dDQUFHOzs7Ozs7Ozs7Ozs7O3NDQUVsRSw4REFBQ2xGOzs4Q0FDQyw4REFBQ0E7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBYStELGdCQUFnQjdELGdCQUFnQitCLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT25GO0dBdFN3QmhDO01BQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFqaXRoXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXExpdmUgVHJhY2tpbmdcXGxpdmUtdHJhY2tpbmdcXHNyY1xcYXBwXFx0cmFjay1zdXBhYmFzZVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcbmltcG9ydCB7IGxvY2F0aW9uU2VydmljZSwgTG9jYXRpb25EYXRhIH0gZnJvbSAnLi4vLi4vLi4vbGliL3N1cGFiYXNlJztcbmltcG9ydCB7IFJlYWx0aW1lQ2hhbm5lbCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5cbi8vIER5bmFtaWNhbGx5IGltcG9ydCB0aGUgbWFwIGNvbXBvbmVudCB0byBhdm9pZCBTU1IgaXNzdWVzXG5jb25zdCBMaXZlTWFwID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9MaXZlTWFwJyksIHtcbiAgc3NyOiBmYWxzZSxcbiAgbG9hZGluZzogKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTEwMFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIG1hcC4uLjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0pO1xuXG5pbnRlcmZhY2UgVHJhY2tpbmdMb2NhdGlvbiB7XG4gIGxhdGl0dWRlOiBudW1iZXI7XG4gIGxvbmdpdHVkZTogbnVtYmVyO1xuICBhY2N1cmFjeTogbnVtYmVyO1xuICB0aW1lc3RhbXA6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVHJhY2tTdXBhYmFzZVBhZ2UoKSB7XG4gIGNvbnN0IFtjdXJyZW50TG9jYXRpb24sIHNldEN1cnJlbnRMb2NhdGlvbl0gPSB1c2VTdGF0ZTxUcmFja2luZ0xvY2F0aW9uIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2NhdGlvbkhpc3RvcnksIHNldExvY2F0aW9uSGlzdG9yeV0gPSB1c2VTdGF0ZTxMb2NhdGlvbkRhdGFbXT4oW10pO1xuICBjb25zdCBbYWN0aXZlRGV2aWNlcywgc2V0QWN0aXZlRGV2aWNlc10gPSB1c2VTdGF0ZTxMb2NhdGlvbkRhdGFbXT4oW10pO1xuICBjb25zdCBbYXV0b0NlbnRlciwgc2V0QXV0b0NlbnRlcl0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dIaXN0b3J5LCBzZXRTaG93SGlzdG9yeV0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dQYXRoLCBzZXRTaG93UGF0aF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2xhc3RVcGRhdGVUaW1lLCBzZXRMYXN0VXBkYXRlVGltZV0gPSB1c2VTdGF0ZTxEYXRlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWREZXZpY2UsIHNldFNlbGVjdGVkRGV2aWNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gIC8vIExvYWQgaW5pdGlhbCBkYXRhXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZEluaXRpYWxEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICAgIFxuICAgICAgICAvLyBHZXQgYWxsIGFjdGl2ZSBkZXZpY2VzXG4gICAgICAgIGNvbnN0IGRldmljZXMgPSBhd2FpdCBsb2NhdGlvblNlcnZpY2UuZ2V0QWN0aXZlRGV2aWNlcyg2MCk7IC8vIExhc3QgNjAgbWludXRlc1xuICAgICAgICBzZXRBY3RpdmVEZXZpY2VzKGRldmljZXMpO1xuICAgICAgICBcbiAgICAgICAgLy8gSWYgd2UgaGF2ZSBkZXZpY2VzLCBzZWxlY3QgdGhlIGZpcnN0IG9uZSBhbmQgbG9hZCBpdHMgZGF0YVxuICAgICAgICBpZiAoZGV2aWNlcy5sZW5ndGggPiAwICYmICFzZWxlY3RlZERldmljZSkge1xuICAgICAgICAgIGNvbnN0IGZpcnN0RGV2aWNlID0gZGV2aWNlc1swXTtcbiAgICAgICAgICBzZXRTZWxlY3RlZERldmljZShmaXJzdERldmljZS5kZXZpY2VfaWQpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIFNldCBjdXJyZW50IGxvY2F0aW9uXG4gICAgICAgICAgc2V0Q3VycmVudExvY2F0aW9uKHtcbiAgICAgICAgICAgIGxhdDogZmlyc3REZXZpY2UubGF0aXR1ZGUsXG4gICAgICAgICAgICBsbmc6IGZpcnN0RGV2aWNlLmxvbmdpdHVkZSxcbiAgICAgICAgICAgIGFjY3VyYWN5OiBmaXJzdERldmljZS5hY2N1cmFjeSxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogZmlyc3REZXZpY2UudGltZXN0YW1wXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gTG9hZCBoaXN0b3J5IGZvciB0aGlzIGRldmljZVxuICAgICAgICAgIGNvbnN0IGhpc3RvcnkgPSBhd2FpdCBsb2NhdGlvblNlcnZpY2UuZ2V0TG9jYXRpb25IaXN0b3J5KGZpcnN0RGV2aWNlLmRldmljZV9pZCwgMTAwKTtcbiAgICAgICAgICBzZXRMb2NhdGlvbkhpc3RvcnkoaGlzdG9yeSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHNldElzQ29ubmVjdGVkKHRydWUpO1xuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgaW5pdGlhbCBkYXRhOicsIGVycik7XG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgZGF0YScpO1xuICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGxvYWRJbml0aWFsRGF0YSgpO1xuICB9LCBbc2VsZWN0ZWREZXZpY2VdKTtcblxuICAvLyBTZXQgdXAgcmVhbC10aW1lIHN1YnNjcmlwdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBzdWJzY3JpcHRpb246IFJlYWx0aW1lQ2hhbm5lbCB8IG51bGwgPSBudWxsO1xuXG4gICAgY29uc3Qgc2V0dXBSZWFsdGltZVN1YnNjcmlwdGlvbiA9ICgpID0+IHtcbiAgICAgIHN1YnNjcmlwdGlvbiA9IGxvY2F0aW9uU2VydmljZS5zdWJzY3JpYmVUb0xvY2F0aW9uVXBkYXRlcygocGF5bG9hZCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygnUmVhbC10aW1lIHVwZGF0ZSByZWNlaXZlZDonLCBwYXlsb2FkKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChwYXlsb2FkLmV2ZW50VHlwZSA9PT0gJ0lOU0VSVCcgfHwgcGF5bG9hZC5ldmVudFR5cGUgPT09ICdVUERBVEUnKSB7XG4gICAgICAgICAgY29uc3QgbmV3TG9jYXRpb24gPSBwYXlsb2FkLm5ldyBhcyBMb2NhdGlvbkRhdGE7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gVXBkYXRlIGN1cnJlbnQgbG9jYXRpb24gaWYgaXQncyBmcm9tIHRoZSBzZWxlY3RlZCBkZXZpY2Ugb3IgaWYgbm8gZGV2aWNlIGlzIHNlbGVjdGVkXG4gICAgICAgICAgaWYgKCFzZWxlY3RlZERldmljZSB8fCBuZXdMb2NhdGlvbi5kZXZpY2VfaWQgPT09IHNlbGVjdGVkRGV2aWNlKSB7XG4gICAgICAgICAgICBzZXRDdXJyZW50TG9jYXRpb24oe1xuICAgICAgICAgICAgICBsYXQ6IG5ld0xvY2F0aW9uLmxhdGl0dWRlLFxuICAgICAgICAgICAgICBsbmc6IG5ld0xvY2F0aW9uLmxvbmdpdHVkZSxcbiAgICAgICAgICAgICAgYWNjdXJhY3k6IG5ld0xvY2F0aW9uLmFjY3VyYWN5LFxuICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ld0xvY2F0aW9uLnRpbWVzdGFtcFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHNldExhc3RVcGRhdGVUaW1lKG5ldyBEYXRlKCkpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBBZGQgdG8gaGlzdG9yeVxuICAgICAgICAgICAgc2V0TG9jYXRpb25IaXN0b3J5KHByZXYgPT4gW25ld0xvY2F0aW9uLCAuLi5wcmV2LnNsaWNlKDAsIDk5KV0pO1xuICAgICAgICAgIH1cbiAgICAgICAgICBcbiAgICAgICAgICAvLyBVcGRhdGUgYWN0aXZlIGRldmljZXMgbGlzdFxuICAgICAgICAgIHNldEFjdGl2ZURldmljZXMocHJldiA9PiB7XG4gICAgICAgICAgICBjb25zdCBleGlzdGluZ0luZGV4ID0gcHJldi5maW5kSW5kZXgoZCA9PiBkLmRldmljZV9pZCA9PT0gbmV3TG9jYXRpb24uZGV2aWNlX2lkKTtcbiAgICAgICAgICAgIGlmIChleGlzdGluZ0luZGV4ID49IDApIHtcbiAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZCA9IFsuLi5wcmV2XTtcbiAgICAgICAgICAgICAgdXBkYXRlZFtleGlzdGluZ0luZGV4XSA9IG5ld0xvY2F0aW9uO1xuICAgICAgICAgICAgICByZXR1cm4gdXBkYXRlZDtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHJldHVybiBbbmV3TG9jYXRpb24sIC4uLnByZXZdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIFxuICAgICAgICAgIHNldElzQ29ubmVjdGVkKHRydWUpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9O1xuXG4gICAgc2V0dXBSZWFsdGltZVN1YnNjcmlwdGlvbigpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChzdWJzY3JpcHRpb24pIHtcbiAgICAgICAgc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW3NlbGVjdGVkRGV2aWNlXSk7XG5cbiAgLy8gSGFuZGxlIGRldmljZSBzZWxlY3Rpb24gY2hhbmdlXG4gIGNvbnN0IGhhbmRsZURldmljZUNoYW5nZSA9IGFzeW5jIChkZXZpY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGRldmljZUlkID09PSBzZWxlY3RlZERldmljZSkgcmV0dXJuO1xuICAgIFxuICAgIHNldFNlbGVjdGVkRGV2aWNlKGRldmljZUlkKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICBcbiAgICAgIC8vIExvYWQgZGF0YSBmb3IgdGhlIHNlbGVjdGVkIGRldmljZVxuICAgICAgY29uc3QgbGF0ZXN0TG9jYXRpb24gPSBhd2FpdCBsb2NhdGlvblNlcnZpY2UuZ2V0TGF0ZXN0TG9jYXRpb24oZGV2aWNlSWQpO1xuICAgICAgaWYgKGxhdGVzdExvY2F0aW9uKSB7XG4gICAgICAgIHNldEN1cnJlbnRMb2NhdGlvbih7XG4gICAgICAgICAgbGF0OiBsYXRlc3RMb2NhdGlvbi5sYXRpdHVkZSxcbiAgICAgICAgICBsbmc6IGxhdGVzdExvY2F0aW9uLmxvbmdpdHVkZSxcbiAgICAgICAgICBhY2N1cmFjeTogbGF0ZXN0TG9jYXRpb24uYWNjdXJhY3ksXG4gICAgICAgICAgdGltZXN0YW1wOiBsYXRlc3RMb2NhdGlvbi50aW1lc3RhbXBcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIExvYWQgaGlzdG9yeVxuICAgICAgY29uc3QgaGlzdG9yeSA9IGF3YWl0IGxvY2F0aW9uU2VydmljZS5nZXRMb2NhdGlvbkhpc3RvcnkoZGV2aWNlSWQsIDEwMCk7XG4gICAgICBzZXRMb2NhdGlvbkhpc3RvcnkoaGlzdG9yeSk7XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgZGV2aWNlIGRhdGE6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgZGV2aWNlIGRhdGEnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0VGltZSA9IChkYXRlOiBEYXRlIHwgbnVsbCkgPT4ge1xuICAgIGlmICghZGF0ZSkgcmV0dXJuICdOZXZlcic7XG4gICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0VGltZXN0YW1wID0gKHRpbWVzdGFtcDogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBEYXRlKHRpbWVzdGFtcCkudG9Mb2NhbGVUaW1lU3RyaW5nKCk7XG4gIH07XG5cbiAgLy8gQ29udmVydCBsb2NhdGlvbiBoaXN0b3J5IHRvIHRoZSBmb3JtYXQgZXhwZWN0ZWQgYnkgTGl2ZU1hcFxuICBjb25zdCBtYXBMb2NhdGlvbnMgPSBsb2NhdGlvbkhpc3RvcnkubWFwKGxvYyA9PiAoe1xuICAgIGxhdGl0dWRlOiBsb2MubGF0aXR1ZGUsXG4gICAgbG9uZ2l0dWRlOiBsb2MubG9uZ2l0dWRlLFxuICAgIGFjY3VyYWN5OiBsb2MuYWNjdXJhY3ksXG4gICAgdGltZXN0YW1wOiBsb2MudGltZXN0YW1wXG4gIH0pKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1zY3JlZW4gZmxleCBmbGV4LWNvbCBiZy1ncmF5LTEwMFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LXNtIGJvcmRlci1iIHAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+8J+TjSBMaXZlIFRyYWNraW5nIChTdXBhYmFzZSk8L2gxPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICB7LyogRGV2aWNlIFNlbGVjdG9yICovfVxuICAgICAgICAgICAge2FjdGl2ZURldmljZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWREZXZpY2V9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVEZXZpY2VDaGFuZ2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+U2VsZWN0IERldmljZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIHthY3RpdmVEZXZpY2VzLm1hcChkZXZpY2UgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2RldmljZS5kZXZpY2VfaWR9IHZhbHVlPXtkZXZpY2UuZGV2aWNlX2lkfT5cbiAgICAgICAgICAgICAgICAgICAge2RldmljZS5kZXZpY2VfaWQuc3Vic3RyaW5nKDAsIDIwKX0uLi5cbiAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBDb25uZWN0aW9uIFN0YXR1cyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtpc0Nvbm5lY3RlZCA/ICdiZy1ncmVlbi01MDAnIDogJ2JnLXJlZC01MDAnfWB9PjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICB7aXNDb25uZWN0ZWQgPyAnQ29ubmVjdGVkJyA6ICdEaXNjb25uZWN0ZWQnfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogU3RhdHVzIEJhciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPHNwYW4+QWN0aXZlIERldmljZXM6IHthY3RpdmVEZXZpY2VzLmxlbmd0aH08L3NwYW4+XG4gICAgICAgICAgICA8c3Bhbj5IaXN0b3J5OiB7bG9jYXRpb25IaXN0b3J5Lmxlbmd0aH0gcG9pbnRzPC9zcGFuPlxuICAgICAgICAgICAge2N1cnJlbnRMb2NhdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgIExhc3QgVXBkYXRlOiB7Zm9ybWF0VGltZXN0YW1wKGN1cnJlbnRMb2NhdGlvbi50aW1lc3RhbXApfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlxuICAgICAgICAgICAgICBFcnJvcjoge2Vycm9yfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRyb2xzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBwLTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17YXV0b0NlbnRlcn1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBdXRvQ2VudGVyKGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+QXV0byBDZW50ZXI8L3NwYW4+XG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICBcbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17c2hvd0hpc3Rvcnl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2hvd0hpc3RvcnkoZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5TaG93IEhpc3Rvcnk8L3NwYW4+XG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICBcbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17c2hvd1BhdGh9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2hvd1BhdGgoZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5TaG93IFBhdGg8L3NwYW4+XG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1hcCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgIHtjdXJyZW50TG9jYXRpb24gPyAoXG4gICAgICAgICAgPExpdmVNYXBcbiAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbj17Y3VycmVudExvY2F0aW9ufVxuICAgICAgICAgICAgbG9jYXRpb25IaXN0b3J5PXtzaG93SGlzdG9yeSA/IG1hcExvY2F0aW9ucyA6IFtdfVxuICAgICAgICAgICAgYXV0b0NlbnRlcj17YXV0b0NlbnRlcn1cbiAgICAgICAgICAgIHNob3dQYXRoPXtzaG93UGF0aH1cbiAgICAgICAgICAvPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIHthY3RpdmVEZXZpY2VzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBtYi0yXCI+8J+TsSBObyBBY3RpdmUgRGV2aWNlczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5TdGFydCBicm9hZGNhc3RpbmcgZnJvbSBhbiBhZG1pbiBkZXZpY2UgdG8gc2VlIGxvY2F0aW9uIHVwZGF0ZXM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgbWItMlwiPvCfk40gU2VsZWN0IGEgRGV2aWNlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPkNob29zZSBhIGRldmljZSBmcm9tIHRoZSBkcm9wZG93biB0byBzdGFydCB0cmFja2luZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTG9jYXRpb24gSW5mbyBQYW5lbCAqL31cbiAgICAgIHtjdXJyZW50TG9jYXRpb24gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5MYXRpdHVkZTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbW9ub1wiPntjdXJyZW50TG9jYXRpb24ubGF0LnRvRml4ZWQoNil9PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPkxvbmdpdHVkZTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbW9ub1wiPntjdXJyZW50TG9jYXRpb24ubG5nLnRvRml4ZWQoNil9PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPkFjY3VyYWN5PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tb25vXCI+e2N1cnJlbnRMb2NhdGlvbi5hY2N1cmFjeS50b0ZpeGVkKDApfW08L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+VXBkYXRlZDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbW9ub1wiPntmb3JtYXRUaW1lc3RhbXAoY3VycmVudExvY2F0aW9uLnRpbWVzdGFtcCl9PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiZHluYW1pYyIsImxvY2F0aW9uU2VydmljZSIsIkxpdmVNYXAiLCJzc3IiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiVHJhY2tTdXBhYmFzZVBhZ2UiLCJjdXJyZW50TG9jYXRpb24iLCJzZXRDdXJyZW50TG9jYXRpb24iLCJsb2NhdGlvbkhpc3RvcnkiLCJzZXRMb2NhdGlvbkhpc3RvcnkiLCJhY3RpdmVEZXZpY2VzIiwic2V0QWN0aXZlRGV2aWNlcyIsImF1dG9DZW50ZXIiLCJzZXRBdXRvQ2VudGVyIiwic2hvd0hpc3RvcnkiLCJzZXRTaG93SGlzdG9yeSIsInNob3dQYXRoIiwic2V0U2hvd1BhdGgiLCJsYXN0VXBkYXRlVGltZSIsInNldExhc3RVcGRhdGVUaW1lIiwiaXNDb25uZWN0ZWQiLCJzZXRJc0Nvbm5lY3RlZCIsImVycm9yIiwic2V0RXJyb3IiLCJzZWxlY3RlZERldmljZSIsInNldFNlbGVjdGVkRGV2aWNlIiwibG9hZEluaXRpYWxEYXRhIiwiZGV2aWNlcyIsImdldEFjdGl2ZURldmljZXMiLCJsZW5ndGgiLCJmaXJzdERldmljZSIsImRldmljZV9pZCIsImxhdCIsImxhdGl0dWRlIiwibG5nIiwibG9uZ2l0dWRlIiwiYWNjdXJhY3kiLCJ0aW1lc3RhbXAiLCJoaXN0b3J5IiwiZ2V0TG9jYXRpb25IaXN0b3J5IiwiZXJyIiwiY29uc29sZSIsIkVycm9yIiwibWVzc2FnZSIsInN1YnNjcmlwdGlvbiIsInNldHVwUmVhbHRpbWVTdWJzY3JpcHRpb24iLCJzdWJzY3JpYmVUb0xvY2F0aW9uVXBkYXRlcyIsInBheWxvYWQiLCJsb2ciLCJldmVudFR5cGUiLCJuZXdMb2NhdGlvbiIsIm5ldyIsIkRhdGUiLCJwcmV2Iiwic2xpY2UiLCJleGlzdGluZ0luZGV4IiwiZmluZEluZGV4IiwiZCIsInVwZGF0ZWQiLCJ1bnN1YnNjcmliZSIsImhhbmRsZURldmljZUNoYW5nZSIsImRldmljZUlkIiwibGF0ZXN0TG9jYXRpb24iLCJnZXRMYXRlc3RMb2NhdGlvbiIsImZvcm1hdFRpbWUiLCJkYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiZm9ybWF0VGltZXN0YW1wIiwibWFwTG9jYXRpb25zIiwibWFwIiwibG9jIiwiaDEiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9wdGlvbiIsImRldmljZSIsInN1YnN0cmluZyIsInNwYW4iLCJsYWJlbCIsImlucHV0IiwidHlwZSIsImNoZWNrZWQiLCJ0b0ZpeGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/track-supabase/page.tsx\n"));

/***/ })

});