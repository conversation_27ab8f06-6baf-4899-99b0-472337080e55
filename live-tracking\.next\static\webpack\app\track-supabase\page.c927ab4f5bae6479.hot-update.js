"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/track-supabase/page",{

/***/ "(app-pages-browser)/./src/app/track-supabase/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/track-supabase/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrackSupabasePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import the map component to avoid SSR issues\nconst LiveMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_LiveMap_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/LiveMap */ \"(app-pages-browser)/./src/components/LiveMap.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\track-supabase\\\\page.tsx -> \" + \"@/components/LiveMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading map...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 5\n        }, undefined)\n});\n_c = LiveMap;\nfunction TrackSupabasePage() {\n    _s();\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [locationHistory, setLocationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeDevices, setActiveDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoCenter, setAutoCenter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPath, setShowPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdateTime, setLastUpdateTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrackSupabasePage.useEffect\": ()=>{\n            const loadInitialData = {\n                \"TrackSupabasePage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setError(null);\n                        // Get all active devices\n                        const devices = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getActiveDevices(60); // Last 60 minutes\n                        setActiveDevices(devices);\n                        // If we have devices, select the first one and load its data\n                        if (devices.length > 0 && !selectedDevice) {\n                            const firstDevice = devices[0];\n                            setSelectedDevice(firstDevice.device_id);\n                            // Set current location\n                            setCurrentLocation({\n                                latitude: firstDevice.latitude,\n                                longitude: firstDevice.longitude,\n                                accuracy: firstDevice.accuracy,\n                                timestamp: firstDevice.timestamp\n                            });\n                            // Load history for this device\n                            const history = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLocationHistory(firstDevice.device_id, 100);\n                            setLocationHistory(history);\n                        }\n                        setIsConnected(true);\n                    } catch (err) {\n                        console.error('Error loading initial data:', err);\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                        setIsConnected(false);\n                    }\n                }\n            }[\"TrackSupabasePage.useEffect.loadInitialData\"];\n            loadInitialData();\n        }\n    }[\"TrackSupabasePage.useEffect\"], [\n        selectedDevice\n    ]);\n    // Set up real-time subscription\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrackSupabasePage.useEffect\": ()=>{\n            let subscription = null;\n            const setupRealtimeSubscription = {\n                \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": ()=>{\n                    subscription = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.subscribeToLocationUpdates({\n                        \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (payload)=>{\n                            console.log('Real-time update received:', payload);\n                            if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {\n                                const newLocation = payload.new;\n                                // Update current location if it's from the selected device or if no device is selected\n                                if (!selectedDevice || newLocation.device_id === selectedDevice) {\n                                    setCurrentLocation({\n                                        latitude: newLocation.latitude,\n                                        longitude: newLocation.longitude,\n                                        accuracy: newLocation.accuracy,\n                                        timestamp: newLocation.timestamp\n                                    });\n                                    setLastUpdateTime(new Date());\n                                    // Add to history\n                                    setLocationHistory({\n                                        \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (prev)=>[\n                                                newLocation,\n                                                ...prev.slice(0, 99)\n                                            ]\n                                    }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                                }\n                                // Update active devices list\n                                setActiveDevices({\n                                    \"TrackSupabasePage.useEffect.setupRealtimeSubscription\": (prev)=>{\n                                        const existingIndex = prev.findIndex({\n                                            \"TrackSupabasePage.useEffect.setupRealtimeSubscription.existingIndex\": (d)=>d.device_id === newLocation.device_id\n                                        }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription.existingIndex\"]);\n                                        if (existingIndex >= 0) {\n                                            const updated = [\n                                                ...prev\n                                            ];\n                                            updated[existingIndex] = newLocation;\n                                            return updated;\n                                        } else {\n                                            return [\n                                                newLocation,\n                                                ...prev\n                                            ];\n                                        }\n                                    }\n                                }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                                setIsConnected(true);\n                            }\n                        }\n                    }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"]);\n                }\n            }[\"TrackSupabasePage.useEffect.setupRealtimeSubscription\"];\n            setupRealtimeSubscription();\n            return ({\n                \"TrackSupabasePage.useEffect\": ()=>{\n                    if (subscription) {\n                        subscription.unsubscribe();\n                    }\n                }\n            })[\"TrackSupabasePage.useEffect\"];\n        }\n    }[\"TrackSupabasePage.useEffect\"], [\n        selectedDevice\n    ]);\n    // Handle device selection change\n    const handleDeviceChange = async (deviceId)=>{\n        if (deviceId === selectedDevice) return;\n        setSelectedDevice(deviceId);\n        try {\n            setError(null);\n            // Load data for the selected device\n            const latestLocation = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLatestLocation(deviceId);\n            if (latestLocation) {\n                setCurrentLocation({\n                    latitude: latestLocation.latitude,\n                    longitude: latestLocation.longitude,\n                    accuracy: latestLocation.accuracy,\n                    timestamp: latestLocation.timestamp\n                });\n            }\n            // Load history\n            const history = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.locationService.getLocationHistory(deviceId, 100);\n            setLocationHistory(history);\n        } catch (err) {\n            console.error('Error loading device data:', err);\n            setError(err instanceof Error ? err.message : 'Failed to load device data');\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return 'Never';\n        return date.toLocaleTimeString();\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString();\n    };\n    // Convert location history to the format expected by LiveMap\n    const mapLocations = locationHistory.map((loc)=>({\n            latitude: loc.latitude,\n            longitude: loc.longitude,\n            accuracy: loc.accuracy,\n            timestamp: loc.timestamp\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-800\",\n                                children: \"\\uD83D\\uDCCD Live Tracking (Supabase)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    activeDevices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedDevice,\n                                        onChange: (e)=>handleDeviceChange(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded-md text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Device\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            activeDevices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: device.device_id,\n                                                    children: [\n                                                        device.device_id.substring(0, 20),\n                                                        \"...\"\n                                                    ]\n                                                }, device.device_id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: isConnected ? 'Connected' : 'Disconnected'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center justify-between text-xs text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Active Devices: \",\n                                            activeDevices.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"History: \",\n                                            locationHistory.length,\n                                            \" points\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Last Update: \",\n                                            formatTimestamp(currentLocation.timestamp)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500\",\n                                children: [\n                                    \"Error: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: autoCenter,\n                                    onChange: (e)=>setAutoCenter(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Auto Center\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: showHistory,\n                                    onChange: (e)=>setShowHistory(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Show History\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: showPath,\n                                    onChange: (e)=>setShowPath(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Show Path\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: currentLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveMap, {\n                    currentLocation: currentLocation,\n                    locationHistory: showHistory ? mapLocations : [],\n                    autoCenter: autoCenter,\n                    showPath: showPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500 mb-4\",\n                            children: activeDevices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg mb-2\",\n                                        children: \"\\uD83D\\uDCF1 No Active Devices\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Start broadcasting from an admin device to see location updates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg mb-2\",\n                                        children: \"\\uD83D\\uDCCD Select a Device\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Choose a device from the dropdown to start tracking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            currentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Latitude\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: currentLocation.lat.toFixed(6)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Longitude\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: currentLocation.lng.toFixed(6)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Accuracy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: [\n                                        currentLocation.accuracy.toFixed(0),\n                                        \"m\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono\",\n                                    children: formatTimestamp(currentLocation.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Live Tracking\\\\live-tracking\\\\src\\\\app\\\\track-supabase\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(TrackSupabasePage, \"hhtiX3x9SpT8hUtsA3Mzd4Ib8iU=\");\n_c1 = TrackSupabasePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"LiveMap\");\n$RefreshReg$(_c1, \"TrackSupabasePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/track-supabase/page.tsx\n"));

/***/ })

});