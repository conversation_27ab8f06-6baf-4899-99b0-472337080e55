# Production Deployment Guide

## 🚀 Deployment Steps

### 1. Build the Application
```bash
npm run build
```

### 2. Environment Variables
Set these environment variables in your production environment:

```bash
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0
```

### 3. Start Production Server
```bash
npm start
```

## 🔧 Server Configuration

### For PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
pm2 start server.js --name "live-tracking"

# Save PM2 configuration
pm2 save
pm2 startup
```

### For Docker
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 🌐 Nginx Configuration (if using reverse proxy)

```nginx
server {
    listen 80;
    server_name tracking.codzz.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Socket.IO specific configuration
    location /socket.io/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 Troubleshooting

### 404 Errors
- Ensure `NODE_ENV=production` is set
- Verify the build completed successfully
- Check that all routes are properly handled by Next.js

### Socket.IO Connection Issues
- Verify WebSocket support in your hosting environment
- Check firewall settings for the port
- Ensure CORS is properly configured

### Performance Optimization
- Use PM2 for process management
- Enable gzip compression in Nginx
- Set up SSL/HTTPS for production

## 📱 Testing Production Build

1. **Local Testing**:
   ```bash
   NODE_ENV=production npm start
   ```

2. **Access URLs**:
   - Admin: `http://your-domain.com/admin`
   - Tracker: `http://your-domain.com/track`
   - Home: `http://your-domain.com/`

## 🔒 Security Considerations

- Use HTTPS in production
- Configure proper CORS origins
- Set up rate limiting
- Use environment variables for sensitive data
- Regular security updates

## 📊 Monitoring

- Monitor server logs: `pm2 logs live-tracking`
- Check server status: `pm2 status`
- Monitor resource usage: `pm2 monit`
