{"name": "live-tracking", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "lint": "next lint"}, "dependencies": {"@types/leaflet": "^1.9.19", "leaflet": "^1.9.4", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}