// Service Worker for Live Tracking App
const CACHE_NAME = 'live-tracking-v1';
const urlsToCache = [
  '/',
  '/admin',
  '/track',
  '/manifest.json'
];

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('Service Worker: Installation complete');
        return self.skipWaiting();
      })
  );
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activation complete');
      return self.clients.claim();
    })
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip socket.io requests
  if (event.request.url.includes('socket.io')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
      .catch(() => {
        // Return offline page for navigation requests
        if (event.request.mode === 'navigate') {
          return caches.match('/');
        }
      })
  );
});

// Background sync for location updates
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'location-sync') {
    event.waitUntil(
      // Handle background location sync if needed
      handleBackgroundLocationSync()
    );
  }
});

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'Location update received',
    icon: '/icon-192x192.png',
    badge: '/icon-72x72.png',
    tag: 'location-update',
    requireInteraction: false,
    silent: true
  };

  event.waitUntil(
    self.registration.showNotification('Live Tracking', options)
  );
});

// Handle background location sync
async function handleBackgroundLocationSync() {
  try {
    // This would handle any queued location updates
    console.log('Service Worker: Handling background location sync');
    
    // Get all clients (open tabs/windows)
    const clients = await self.clients.matchAll();
    
    // Notify clients about sync
    clients.forEach(client => {
      client.postMessage({
        type: 'BACKGROUND_SYNC',
        timestamp: Date.now()
      });
    });
    
  } catch (error) {
    console.error('Service Worker: Background sync failed', error);
  }
}

// Handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'REGISTER_BACKGROUND_SYNC') {
    // Register background sync
    self.registration.sync.register('location-sync')
      .then(() => {
        console.log('Service Worker: Background sync registered');
      })
      .catch((error) => {
        console.error('Service Worker: Background sync registration failed', error);
      });
  }
});
