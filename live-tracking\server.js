const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { Server } = require('socket.io');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || (dev ? 'localhost' : '0.0.0.0');
const port = process.env.PORT || 3000;

// Create Next.js app
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  // Create HTTP server
  const httpServer = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Create Socket.IO server
  const io = new Server(httpServer, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    }
  });

  // Store active connections and location data
  const activeConnections = new Map();
  let currentLocation = null;
  let locationHistory = [];
  const MAX_HISTORY = 100;

  io.on('connection', (socket) => {
    console.log(`Client connected: ${socket.id}`);
    
    // Store connection info
    activeConnections.set(socket.id, {
      id: socket.id,
      type: null, // 'admin' or 'tracker'
      connectedAt: new Date()
    });

    // Handle client joining with role
    socket.on('join', (role) => {
      console.log(`Client ${socket.id} joined as ${role}`);
      activeConnections.get(socket.id).type = role;

      if (role === 'tracker') {
        // Send current location if available
        if (currentLocation) {
          socket.emit('location-update', currentLocation);
        }

        // Send location history
        if (locationHistory.length > 0) {
          socket.emit('location-history', locationHistory);
        }
      }

      // Send updated connection count to all clients
      const connectionCount = {
        total: activeConnections.size,
        admins: Array.from(activeConnections.values()).filter(c => c.type === 'admin').length,
        trackers: Array.from(activeConnections.values()).filter(c => c.type === 'tracker').length
      };
      io.emit('connection-count', connectionCount);
    });

    // Handle location updates from admin
    socket.on('location-update', (locationData) => {
      const connection = activeConnections.get(socket.id);
      if (connection && connection.type === 'admin') {
        // Validate location data - support both lat/lng and latitude/longitude formats
        const lat = locationData.lat || locationData.latitude;
        const lng = locationData.lng || locationData.longitude;

        if (locationData && typeof lat === 'number' && typeof lng === 'number') {
          // Normalize location data format
          const enrichedLocation = {
            lat,
            lng,
            accuracy: locationData.accuracy || 0,
            timestamp: new Date().toISOString(),
            receivedAt: Date.now()
          };

          // Update current location
          currentLocation = enrichedLocation;

          // Add to history
          locationHistory.push(enrichedLocation);
          if (locationHistory.length > MAX_HISTORY) {
            locationHistory.shift();
          }

          // Broadcast to all tracker clients
          socket.broadcast.emit('location-update', enrichedLocation);

          console.log(`Location update from ${socket.id}:`, {
            lat: enrichedLocation.lat,
            lng: enrichedLocation.lng,
            accuracy: enrichedLocation.accuracy
          });
        }
      }
    });

    // Handle connection status updates
    socket.on('connection-status', (status) => {
      const connection = activeConnections.get(socket.id);
      if (connection) {
        connection.status = status;
        connection.lastSeen = new Date();
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`Client disconnected: ${socket.id}, reason: ${reason}`);
      activeConnections.delete(socket.id);
      
      // Notify other clients about connection status
      io.emit('connection-count', {
        total: activeConnections.size,
        admins: Array.from(activeConnections.values()).filter(c => c.type === 'admin').length,
        trackers: Array.from(activeConnections.values()).filter(c => c.type === 'tracker').length
      });
    });

    // Send initial connection count
    socket.emit('connection-count', {
      total: activeConnections.size,
      admins: Array.from(activeConnections.values()).filter(c => c.type === 'admin').length,
      trackers: Array.from(activeConnections.values()).filter(c => c.type === 'tracker').length
    });
  });

  // Start server
  httpServer.listen(port, hostname, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
    console.log(`> Socket.IO server running`);
    console.log(`> Environment: ${dev ? 'development' : 'production'}`);
  });
});
