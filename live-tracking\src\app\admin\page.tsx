'use client';

import { useEffect, useState, useRef } from 'react';
import { useSocket } from '@/hooks/useSocket';
import { useGeolocation } from '@/hooks/useGeolocation';

export default function AdminPage() {
  const [isActive, setIsActive] = useState(false);
  const [wakeLock, setWakeLock] = useState<WakeLockSentinel | null>(null);
  const [stats, setStats] = useState({
    locationsSent: 0,
    lastSentAt: null as Date | null,
    startedAt: null as Date | null
  });

  const { 
    socket, 
    isConnected, 
    connectionCount, 
    joinAsAdmin, 
    sendLocation 
  } = useSocket();

  const {
    location,
    error,
    isLoading,
    isSupported,
    isTracking,
    startTracking,
    stopTracking,
    clearError
  } = useGeolocation({
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 5000,
    updateInterval: 3000
  });

  const lastLocationRef = useRef<string>('');

  // Join as admin when socket connects
  useEffect(() => {
    if (isConnected) {
      joinAsAdmin();
    }
  }, [isConnected, joinAsAdmin]);

  // Send location updates when location changes
  useEffect(() => {
    if (location && isActive && isConnected) {
      const locationKey = `${location.latitude}-${location.longitude}-${location.timestamp}`;
      
      // Avoid sending duplicate locations
      if (locationKey !== lastLocationRef.current) {
        sendLocation(location);
        lastLocationRef.current = locationKey;
        
        setStats(prev => ({
          ...prev,
          locationsSent: prev.locationsSent + 1,
          lastSentAt: new Date()
        }));
      }
    }
  }, [location, isActive, isConnected, sendLocation]);

  // Request wake lock to prevent screen from sleeping
  const requestWakeLock = async () => {
    try {
      if ('wakeLock' in navigator) {
        const lock = await navigator.wakeLock.request('screen');
        setWakeLock(lock);
        console.log('Wake lock acquired');
        
        lock.addEventListener('release', () => {
          console.log('Wake lock released');
          setWakeLock(null);
        });
      }
    } catch (err) {
      console.error('Failed to acquire wake lock:', err);
    }
  };

  // Release wake lock
  const releaseWakeLock = () => {
    if (wakeLock) {
      wakeLock.release();
      setWakeLock(null);
    }
  };

  const handleStart = async () => {
    if (!isSupported) {
      alert('Geolocation is not supported on this device');
      return;
    }

    try {
      setIsActive(true);
      setStats(prev => ({ ...prev, startedAt: new Date(), locationsSent: 0 }));
      
      await requestWakeLock();
      startTracking();
    } catch (err) {
      console.error('Failed to start tracking:', err);
      setIsActive(false);
    }
  };

  const handleStop = () => {
    setIsActive(false);
    stopTracking();
    releaseWakeLock();
  };

  const formatTime = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  const formatDuration = (startDate: Date | null) => {
    if (!startDate) return '0s';
    const duration = Math.floor((Date.now() - startDate.getTime()) / 1000);
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-4">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-xl p-6">
          <h1 className="text-2xl font-bold text-center mb-6 text-gray-800">
            📍 Admin Panel
          </h1>

          {/* Connection Status */}
          <div className="mb-6 p-4 rounded-lg bg-gray-50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">Connection</span>
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            </div>
            <div className="text-xs text-gray-500">
              <div>Status: {isConnected ? 'Connected' : 'Disconnected'}</div>
              <div>Trackers: {connectionCount.trackers}</div>
              <div>Total: {connectionCount.total}</div>
            </div>
          </div>

          {/* Location Status */}
          <div className="mb-6 p-4 rounded-lg bg-gray-50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">Location</span>
              <div className={`w-3 h-3 rounded-full ${
                isTracking ? 'bg-green-500' : 
                isLoading ? 'bg-yellow-500' : 
                'bg-gray-400'
              }`}></div>
            </div>
            
            {location && (
              <div className="text-xs text-gray-500 space-y-1">
                <div>Lat: {location.latitude.toFixed(6)}</div>
                <div>Lng: {location.longitude.toFixed(6)}</div>
                <div>Accuracy: {location.accuracy?.toFixed(0)}m</div>
                <div>Updated: {formatTime(location.timestamp ? new Date(location.timestamp) : null)}</div>
              </div>
            )}
            
            {error && (
              <div className="text-xs text-red-500 mt-2">
                Error: {error.message}
                <button 
                  onClick={clearError}
                  className="ml-2 text-blue-500 underline"
                >
                  Clear
                </button>
              </div>
            )}
          </div>

          {/* Stats */}
          <div className="mb-6 p-4 rounded-lg bg-gray-50">
            <div className="text-sm font-medium text-gray-600 mb-2">Statistics</div>
            <div className="text-xs text-gray-500 space-y-1">
              <div>Locations sent: {stats.locationsSent}</div>
              <div>Last sent: {formatTime(stats.lastSentAt)}</div>
              <div>Running for: {formatDuration(stats.startedAt)}</div>
              <div>Wake lock: {wakeLock ? 'Active' : 'Inactive'}</div>
            </div>
          </div>

          {/* Controls */}
          <div className="space-y-4">
            {!isActive ? (
              <button
                onClick={handleStart}
                disabled={!isSupported || !isConnected}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors"
              >
                {!isSupported ? 'Location Not Supported' :
                 !isConnected ? 'Connecting...' :
                 'Start Broadcasting'}
              </button>
            ) : (
              <button
                onClick={handleStop}
                className="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors"
              >
                Stop Broadcasting
              </button>
            )}

            {isActive && (
              <div className="text-center">
                <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Broadcasting Live
                </div>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Instructions:</h3>
            <ul className="text-xs text-blue-600 space-y-1">
              <li>• Keep this page open and active</li>
              <li>• Allow location permissions when prompted</li>
              <li>• Keep your device charged</li>
              <li>• Avoid switching apps frequently</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
