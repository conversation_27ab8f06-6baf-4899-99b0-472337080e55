import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-xl p-8 text-center">
          <div className="text-6xl mb-6">📍</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Live Tracking
          </h1>
          <p className="text-gray-600 mb-8">
            Real-time location sharing between devices
          </p>

          <div className="space-y-4">
            <Link
              href="/admin"
              className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-6 rounded-lg transition-colors"
            >
              📱 Admin Panel
              <div className="text-sm font-normal opacity-90 mt-1">
                Start broadcasting your location
              </div>
            </Link>

            <Link
              href="/track"
              className="block w-full bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-lg transition-colors"
            >
              🗺️ Live Tracking
              <div className="text-sm font-normal opacity-90 mt-1">
                View real-time location updates
              </div>
            </Link>
          </div>

          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-800 mb-2">How it works:</h3>
            <ol className="text-xs text-gray-600 text-left space-y-1">
              <li>1. Open Admin Panel on Phone A</li>
              <li>2. Allow location permissions and start broadcasting</li>
              <li>3. Open Live Tracking on Phone B</li>
              <li>4. View real-time location updates on the map</li>
            </ol>
          </div>

          <div className="mt-6 text-xs text-gray-500">
            <p>⚠️ Keep both pages open for continuous tracking</p>
            <p>🔋 Ensure devices stay charged during use</p>
          </div>
        </div>
      </div>
    </div>
  );
}
