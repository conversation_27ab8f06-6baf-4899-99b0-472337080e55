'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { locationService, LocationData } from '../../../lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

// Dynamically import the map component to avoid SSR issues
const LiveMap = dynamic(() => import('@/components/LiveMap'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="text-gray-600">Loading map...</div>
      </div>
    </div>
  )
});

interface TrackingLocation {
  lat: number;
  lng: number;
  accuracy: number;
  timestamp: string;
}

export default function TrackSupabasePage() {
  const [currentLocation, setCurrentLocation] = useState<TrackingLocation | null>(null);
  const [locationHistory, setLocationHistory] = useState<LocationData[]>([]);
  const [activeDevices, setActiveDevices] = useState<LocationData[]>([]);
  const [autoCenter, setAutoCenter] = useState(true);
  const [showHistory, setShowHistory] = useState(true);
  const [showPath, setShowPath] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDevice, setSelectedDevice] = useState<string>('');

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setError(null);
        
        // Get all active devices
        const devices = await locationService.getActiveDevices(60); // Last 60 minutes
        setActiveDevices(devices);
        
        // If we have devices, select the first one and load its data
        if (devices.length > 0 && !selectedDevice) {
          const firstDevice = devices[0];
          setSelectedDevice(firstDevice.device_id);
          
          // Set current location
          setCurrentLocation({
            lat: firstDevice.latitude,
            lng: firstDevice.longitude,
            accuracy: firstDevice.accuracy,
            timestamp: firstDevice.timestamp
          });
          
          // Load history for this device
          const history = await locationService.getLocationHistory(firstDevice.device_id, 100);
          setLocationHistory(history);
        }
        
        setIsConnected(true);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
        setIsConnected(false);
      }
    };

    loadInitialData();
  }, [selectedDevice]);

  // Set up real-time subscription
  useEffect(() => {
    let subscription: RealtimeChannel | null = null;

    const setupRealtimeSubscription = () => {
      subscription = locationService.subscribeToLocationUpdates((payload) => {
        console.log('Real-time update received:', payload);
        
        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
          const newLocation = payload.new as LocationData;
          
          // Update current location if it's from the selected device or if no device is selected
          if (!selectedDevice || newLocation.device_id === selectedDevice) {
            setCurrentLocation({
              lat: newLocation.latitude,
              lng: newLocation.longitude,
              accuracy: newLocation.accuracy,
              timestamp: newLocation.timestamp
            });
            
            setLastUpdateTime(new Date());
            
            // Add to history
            setLocationHistory(prev => [newLocation, ...prev.slice(0, 99)]);
          }
          
          // Update active devices list
          setActiveDevices(prev => {
            const existingIndex = prev.findIndex(d => d.device_id === newLocation.device_id);
            if (existingIndex >= 0) {
              const updated = [...prev];
              updated[existingIndex] = newLocation;
              return updated;
            } else {
              return [newLocation, ...prev];
            }
          });
          
          setIsConnected(true);
        }
      });
    };

    setupRealtimeSubscription();

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [selectedDevice]);

  // Handle device selection change
  const handleDeviceChange = async (deviceId: string) => {
    if (deviceId === selectedDevice) return;
    
    setSelectedDevice(deviceId);
    
    try {
      setError(null);
      
      // Load data for the selected device
      const latestLocation = await locationService.getLatestLocation(deviceId);
      if (latestLocation) {
        setCurrentLocation({
          lat: latestLocation.latitude,
          lng: latestLocation.longitude,
          accuracy: latestLocation.accuracy,
          timestamp: latestLocation.timestamp
        });
      }
      
      // Load history
      const history = await locationService.getLocationHistory(deviceId, 100);
      setLocationHistory(history);
      
    } catch (err) {
      console.error('Error loading device data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load device data');
    }
  };

  const formatTime = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Convert location history to the format expected by LiveMap
  const mapLocations = locationHistory.map(loc => ({
    lat: loc.latitude,
    lng: loc.longitude,
    accuracy: loc.accuracy,
    timestamp: loc.timestamp
  }));

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">📍 Live Tracking (Supabase)</h1>
          
          <div className="flex items-center space-x-4">
            {/* Device Selector */}
            {activeDevices.length > 0 && (
              <select
                value={selectedDevice}
                onChange={(e) => handleDeviceChange(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="">Select Device</option>
                {activeDevices.map(device => (
                  <option key={device.device_id} value={device.device_id}>
                    {device.device_id.substring(0, 20)}...
                  </option>
                ))}
              </select>
            )}
            
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
        
        {/* Status Bar */}
        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>Active Devices: {activeDevices.length}</span>
            <span>History: {locationHistory.length} points</span>
            {currentLocation && (
              <span>
                Last Update: {formatTimestamp(currentLocation.timestamp)}
              </span>
            )}
          </div>
          
          {error && (
            <div className="text-red-500">
              Error: {error}
            </div>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white border-b p-2">
        <div className="flex items-center justify-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoCenter}
              onChange={(e) => setAutoCenter(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Auto Center</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showHistory}
              onChange={(e) => setShowHistory(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Show History</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showPath}
              onChange={(e) => setShowPath(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Show Path</span>
          </label>
        </div>
      </div>

      {/* Map */}
      <div className="flex-1 relative">
        {currentLocation ? (
          <LiveMap
            currentLocation={currentLocation}
            locationHistory={showHistory ? mapLocations : []}
            autoCenter={autoCenter}
            showPath={showPath}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-500 mb-4">
                {activeDevices.length === 0 ? (
                  <>
                    <div className="text-lg mb-2">📱 No Active Devices</div>
                    <div className="text-sm">Start broadcasting from an admin device to see location updates</div>
                  </>
                ) : (
                  <>
                    <div className="text-lg mb-2">📍 Select a Device</div>
                    <div className="text-sm">Choose a device from the dropdown to start tracking</div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Location Info Panel */}
      {currentLocation && (
        <div className="bg-white border-t p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-500">Latitude</div>
              <div className="font-mono">{currentLocation.lat.toFixed(6)}</div>
            </div>
            <div>
              <div className="text-gray-500">Longitude</div>
              <div className="font-mono">{currentLocation.lng.toFixed(6)}</div>
            </div>
            <div>
              <div className="text-gray-500">Accuracy</div>
              <div className="font-mono">{currentLocation.accuracy.toFixed(0)}m</div>
            </div>
            <div>
              <div className="text-gray-500">Updated</div>
              <div className="font-mono">{formatTimestamp(currentLocation.timestamp)}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
