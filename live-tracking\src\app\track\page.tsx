'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useSocket, LocationData } from '@/hooks/useSocket';

// Dynamically import the map component to avoid SSR issues
const LiveMap = dynamic(() => import('@/components/LiveMap'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="text-gray-600">Loading map...</div>
      </div>
    </div>
  )
});

export default function TrackPage() {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [locationHistory, setLocationHistory] = useState<LocationData[]>([]);
  const [autoCenter, setAutoCenter] = useState(true);
  const [showHistory, setShowHistory] = useState(true);
  const [showPath, setShowPath] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  const { 
    socket, 
    isConnected, 
    connectionCount, 
    joinAsTracker 
  } = useSocket();

  // Join as tracker when socket connects
  useEffect(() => {
    if (isConnected) {
      joinAsTracker();
    }
  }, [isConnected, joinAsTracker]);

  // Listen for location updates
  useEffect(() => {
    if (!socket) return;

    const handleLocationUpdate = (location: LocationData) => {
      console.log('Received location update:', location);
      setCurrentLocation(location);
      setLastUpdateTime(new Date());
      
      // Add to history (keep last 50 locations)
      setLocationHistory(prev => {
        const newHistory = [...prev, location];
        return newHistory.slice(-50);
      });
    };

    const handleLocationHistory = (history: LocationData[]) => {
      console.log('Received location history:', history.length, 'points');
      setLocationHistory(history);
      if (history.length > 0) {
        setCurrentLocation(history[history.length - 1]);
      }
    };

    socket.on('location-update', handleLocationUpdate);
    socket.on('location-history', handleLocationHistory);

    return () => {
      socket.off('location-update', handleLocationUpdate);
      socket.off('location-history', handleLocationHistory);
    };
  }, [socket]);

  const formatTime = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  const getTimeSinceLastUpdate = () => {
    if (!lastUpdateTime) return 'Never';
    const seconds = Math.floor((Date.now() - lastUpdateTime.getTime()) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  const clearHistory = () => {
    setLocationHistory([]);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">📍 Live Tracking</h1>
          
          <div className="flex items-center space-x-4">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>

            {/* Admin Status */}
            <div className="text-sm text-gray-600">
              Admins: {connectionCount.admins}
            </div>
          </div>
        </div>

        {/* Status Bar */}
        <div className="mt-3 flex items-center justify-between text-sm text-gray-500">
          <div>
            Last update: {getTimeSinceLastUpdate()}
          </div>
          <div>
            History: {locationHistory.length} points
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white border-b p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoCenter}
                onChange={(e) => setAutoCenter(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Auto Center</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showHistory}
                onChange={(e) => setShowHistory(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Show History</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showPath}
                onChange={(e) => setShowPath(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Show Path</span>
            </label>
          </div>

          <button
            onClick={clearHistory}
            className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
          >
            Clear History
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        {!isConnected && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[2000]">
            <div className="bg-white rounded-lg p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <div className="text-gray-800 font-medium">Connecting to server...</div>
              <div className="text-gray-600 text-sm mt-2">
                Make sure the admin is broadcasting
              </div>
            </div>
          </div>
        )}

        {isConnected && connectionCount.admins === 0 && (
          <div className="absolute inset-0 bg-yellow-50 flex items-center justify-center z-[2000]">
            <div className="bg-white rounded-lg p-6 text-center border border-yellow-200">
              <div className="text-yellow-600 text-4xl mb-4">⚠️</div>
              <div className="text-gray-800 font-medium">No admin broadcasting</div>
              <div className="text-gray-600 text-sm mt-2">
                Waiting for admin to start location sharing...
              </div>
            </div>
          </div>
        )}

        <LiveMap
          currentLocation={currentLocation}
          locationHistory={locationHistory}
          autoCenter={autoCenter}
          showHistory={showHistory}
          showPath={showPath}
          className="w-full h-full"
        />
      </div>

      {/* Footer Info */}
      {currentLocation && (
        <div className="bg-white border-t p-3">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-500">Latitude</div>
              <div className="font-mono">{currentLocation.latitude.toFixed(6)}</div>
            </div>
            <div>
              <div className="text-gray-500">Longitude</div>
              <div className="font-mono">{currentLocation.longitude.toFixed(6)}</div>
            </div>
            <div>
              <div className="text-gray-500">Accuracy</div>
              <div className="font-mono">
                {currentLocation.accuracy ? `±${currentLocation.accuracy.toFixed(0)}m` : 'N/A'}
              </div>
            </div>
            <div>
              <div className="text-gray-500">Speed</div>
              <div className="font-mono">
                {currentLocation.speed ? `${(currentLocation.speed * 3.6).toFixed(1)} km/h` : 'N/A'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
