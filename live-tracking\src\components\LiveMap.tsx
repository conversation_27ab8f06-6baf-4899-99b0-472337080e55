'use client';

import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
// Define LocationData interface for this component
interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: string;
  speed?: number;
  altitude?: number;
}

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icon for current location
const currentLocationIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="8" fill="#3B82F6" stroke="#FFFFFF" stroke-width="3"/>
      <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>
    </svg>
  `),
  iconSize: [24, 24],
  iconAnchor: [12, 12],
  popupAnchor: [0, -12],
});

// Custom marker icon for location history
const historyLocationIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="8" cy="8" r="6" fill="#10B981" stroke="#FFFFFF" stroke-width="2"/>
      <circle cx="8" cy="8" r="2" fill="#FFFFFF"/>
    </svg>
  `),
  iconSize: [16, 16],
  iconAnchor: [8, 8],
  popupAnchor: [0, -8],
});

interface MapControllerProps {
  currentLocation: LocationData | null;
  autoCenter: boolean;
}

// Component to control map view
function MapController({ currentLocation, autoCenter }: MapControllerProps) {
  const map = useMap();
  const hasInitializedRef = useRef(false);

  useEffect(() => {
    if (currentLocation && autoCenter) {
      const { latitude, longitude } = currentLocation;
      
      if (!hasInitializedRef.current) {
        // Initial zoom to location
        map.setView([latitude, longitude], 16);
        hasInitializedRef.current = true;
      } else {
        // Smooth pan to new location
        map.panTo([latitude, longitude], {
          animate: true,
          duration: 1
        });
      }
    }
  }, [currentLocation, autoCenter, map]);

  return null;
}

interface LiveMapProps {
  currentLocation: LocationData | null;
  locationHistory: LocationData[];
  autoCenter?: boolean;
  showHistory?: boolean;
  showPath?: boolean;
  className?: string;
}

export default function LiveMap({
  currentLocation,
  locationHistory,
  autoCenter = true,
  showHistory = true,
  showPath = true,
  className = ''
}: LiveMapProps) {
  const [mapReady, setMapReady] = useState(false);

  // Debug logging
  console.log('LiveMap props:', { currentLocation, locationHistory, autoCenter, showHistory, showPath });

  // Default center (you can change this to your preferred default location)
  const defaultCenter: [number, number] = [40.7128, -74.0060]; // New York City
  const defaultZoom = 13;

  // Calculate map center
  const mapCenter = currentLocation
    ? [currentLocation.latitude, currentLocation.longitude] as [number, number]
    : defaultCenter;

  console.log('Map center calculated:', mapCenter);

  // Create path from location history
  const pathCoordinates = showPath && locationHistory.length > 1
    ? locationHistory.map(loc => [loc.latitude, loc.longitude] as [number, number])
    : [];

  const formatLocationInfo = (location: LocationData) => {
    const time = location.timestamp ? new Date(location.timestamp).toLocaleTimeString() : 'Unknown';
    const lat = location.latitude ?? 0;
    const lng = location.longitude ?? 0;
    const acc = location.accuracy ?? 0;

    return `
      <div>
        <strong>Location Update</strong><br/>
        <strong>Time:</strong> ${time}<br/>
        <strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}<br/>
        ${acc > 0 ? `<strong>Accuracy:</strong> ${acc.toFixed(0)}m<br/>` : ''}
        ${location.speed ? `<strong>Speed:</strong> ${(location.speed * 3.6).toFixed(1)} km/h<br/>` : ''}
        ${location.altitude ? `<strong>Altitude:</strong> ${location.altitude.toFixed(0)}m<br/>` : ''}
      </div>
    `;
  };

  return (
    <div className={`relative ${className}`}>
      <MapContainer
        center={mapCenter}
        zoom={defaultZoom}
        className="w-full h-full"
        whenReady={() => setMapReady(true)}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {mapReady && (
          <MapController 
            currentLocation={currentLocation} 
            autoCenter={autoCenter} 
          />
        )}

        {/* Current location marker */}
        {currentLocation && (
          <Marker
            position={[currentLocation.latitude, currentLocation.longitude]}
            icon={currentLocationIcon}
          >
            <Popup>
              <div dangerouslySetInnerHTML={{ __html: formatLocationInfo(currentLocation) }} />
            </Popup>
          </Marker>
        )}

        {/* Location history markers */}
        {showHistory && locationHistory.slice(0, -1).map((location, index) => (
          <Marker
            key={`${location.latitude}-${location.longitude}-${index}`}
            position={[location.latitude, location.longitude]}
            icon={historyLocationIcon}
          >
            <Popup>
              <div dangerouslySetInnerHTML={{ __html: formatLocationInfo(location) }} />
            </Popup>
          </Marker>
        ))}

        {/* Path line */}
        {showPath && pathCoordinates.length > 1 && (
          <Polyline
            positions={pathCoordinates}
            color="#3B82F6"
            weight={3}
            opacity={0.7}
          />
        )}
      </MapContainer>

      {/* Map controls overlay */}
      <div className="absolute top-4 right-4 z-[1000] space-y-2">
        {currentLocation && (
          <div className="bg-white rounded-lg shadow-lg p-2 text-xs">
            <div className="font-medium text-gray-800">Current Location</div>
            <div className="text-gray-600">
              {(currentLocation.latitude ?? 0).toFixed(6)}, {(currentLocation.longitude ?? 0).toFixed(6)}
            </div>
            {currentLocation.accuracy && (
              <div className="text-gray-500">±{currentLocation.accuracy.toFixed(0)}m</div>
            )}
          </div>
        )}
        
        <div className="bg-white rounded-lg shadow-lg p-2 text-xs">
          <div className="font-medium text-gray-800">History</div>
          <div className="text-gray-600">{locationHistory.length} points</div>
        </div>
      </div>
    </div>
  );
}
