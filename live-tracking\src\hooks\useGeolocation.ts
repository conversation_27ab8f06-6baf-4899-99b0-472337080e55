import { useState, useEffect, useRef, useCallback } from 'react';
import { LocationData } from './useSocket';

export interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  updateInterval?: number;
  autoStart?: boolean;
}

export interface GeolocationError {
  code: number;
  message: string;
  timestamp: number;
}

export interface UseGeolocationReturn {
  location: LocationData | null;
  error: GeolocationError | null;
  isLoading: boolean;
  isSupported: boolean;
  isTracking: boolean;
  startTracking: () => void;
  stopTracking: () => void;
  getCurrentLocation: () => Promise<LocationData>;
  clearError: () => void;
}

const DEFAULT_OPTIONS: Required<GeolocationOptions> = {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 60000,
  updateInterval: 5000,
  autoStart: false
};

export const useGeolocation = (options: GeolocationOptions = {}): UseGeolocationReturn => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  const [location, setLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<GeolocationError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTracking, setIsTracking] = useState(false);
  
  const watchIdRef = useRef<number | null>(null);
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  
  const isSupported = 'geolocation' in navigator;

  const handleSuccess = useCallback((position: GeolocationPosition) => {
    const locationData: LocationData = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      altitude: position.coords.altitude || undefined,
      altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
      heading: position.coords.heading || undefined,
      speed: position.coords.speed || undefined,
      timestamp: new Date(position.timestamp).toISOString()
    };

    setLocation(locationData);
    setError(null);
    setIsLoading(false);
  }, []);

  const handleError = useCallback((err: GeolocationPositionError) => {
    const errorData: GeolocationError = {
      code: err.code,
      message: getErrorMessage(err.code),
      timestamp: Date.now()
    };

    setError(errorData);
    setIsLoading(false);
    console.error('Geolocation error:', errorData);
  }, []);

  const getErrorMessage = (code: number): string => {
    switch (code) {
      case 1:
        return 'Location access denied by user';
      case 2:
        return 'Location information unavailable';
      case 3:
        return 'Location request timeout';
      default:
        return 'Unknown location error';
    }
  };

  const getCurrentLocation = useCallback((): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!isSupported) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      setIsLoading(true);
      setError(null);

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude || undefined,
            altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
            heading: position.coords.heading || undefined,
            speed: position.coords.speed || undefined,
            timestamp: new Date(position.timestamp).toISOString()
          };
          
          setLocation(locationData);
          setIsLoading(false);
          resolve(locationData);
        },
        (err) => {
          handleError(err);
          reject(err);
        },
        {
          enableHighAccuracy: opts.enableHighAccuracy,
          timeout: opts.timeout,
          maximumAge: opts.maximumAge
        }
      );
    });
  }, [isSupported, opts, handleError]);

  const startTracking = useCallback(() => {
    if (!isSupported || isTracking) return;

    setIsTracking(true);
    setIsLoading(true);
    setError(null);

    // Use watchPosition for continuous tracking
    watchIdRef.current = navigator.geolocation.watchPosition(
      handleSuccess,
      handleError,
      {
        enableHighAccuracy: opts.enableHighAccuracy,
        timeout: opts.timeout,
        maximumAge: opts.maximumAge
      }
    );

    // Fallback: Use setInterval for additional updates if watchPosition is unreliable
    if (opts.updateInterval > 0) {
      intervalIdRef.current = setInterval(() => {
        navigator.geolocation.getCurrentPosition(
          handleSuccess,
          (err) => {
            // Don't update error state for interval-based requests to avoid spam
            console.warn('Interval location update failed:', err);
          },
          {
            enableHighAccuracy: opts.enableHighAccuracy,
            timeout: opts.timeout,
            maximumAge: opts.maximumAge
          }
        );
      }, opts.updateInterval);
    }
  }, [isSupported, isTracking, opts, handleSuccess, handleError]);

  const stopTracking = useCallback(() => {
    if (!isTracking) return;

    setIsTracking(false);
    setIsLoading(false);

    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }

    if (intervalIdRef.current !== null) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  }, [isTracking]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-start tracking if enabled
  useEffect(() => {
    if (opts.autoStart && isSupported) {
      startTracking();
    }

    return () => {
      stopTracking();
    };
  }, [opts.autoStart, isSupported, startTracking, stopTracking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTracking();
    };
  }, [stopTracking]);

  return {
    location,
    error,
    isLoading,
    isSupported,
    isTracking,
    startTracking,
    stopTracking,
    getCurrentLocation,
    clearError
  };
};
