import { useState, useEffect, useCallback, useRef } from 'react';

export interface MobileOptimizationOptions {
  enableWakeLock?: boolean;
  enableVisibilityHandling?: boolean;
  enableBatteryOptimization?: boolean;
  heartbeatInterval?: number;
}

export interface MobileOptimizationReturn {
  isVisible: boolean;
  wakeLock: WakeLockSentinel | null;
  batteryInfo: BatteryInfo | null;
  requestWakeLock: () => Promise<boolean>;
  releaseWakeLock: () => void;
  isWakeLockSupported: boolean;
  isBatteryApiSupported: boolean;
  startHeartbeat: (callback: () => void) => void;
  stopHeartbeat: () => void;
}

interface BatteryInfo {
  charging: boolean;
  level: number;
  chargingTime: number;
  dischargingTime: number;
}

const DEFAULT_OPTIONS: Required<MobileOptimizationOptions> = {
  enableWakeLock: true,
  enableVisibilityHandling: true,
  enableBatteryOptimization: true,
  heartbeatInterval: 30000 // 30 seconds
};

export const useMobileOptimization = (
  options: MobileOptimizationOptions = {}
): MobileOptimizationReturn => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  const [isVisible, setIsVisible] = useState(true);
  const [wakeLock, setWakeLock] = useState<WakeLockSentinel | null>(null);
  const [batteryInfo, setBatteryInfo] = useState<BatteryInfo | null>(null);

  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatCallbackRef = useRef<(() => void) | null>(null);

  const isWakeLockSupported = 'wakeLock' in navigator;
  const isBatteryApiSupported = 'getBattery' in navigator;

  // Wake Lock Management
  const requestWakeLock = useCallback(async (): Promise<boolean> => {
    if (!isWakeLockSupported || !opts.enableWakeLock) {
      console.warn('Wake Lock API not supported or disabled');
      return false;
    }

    try {
      const lock = await navigator.wakeLock.request('screen');
      setWakeLock(lock);

      lock.addEventListener('release', () => {
        console.log('Wake lock released');
        setWakeLock(null);
      });

      console.log('Wake lock acquired');
      return true;
    } catch (err) {
      console.error('Failed to acquire wake lock:', err);
      return false;
    }
  }, [isWakeLockSupported, opts.enableWakeLock]);

  const releaseWakeLock = useCallback(() => {
    if (wakeLock) {
      wakeLock.release();
      setWakeLock(null);
    }
  }, [wakeLock]);

  // Battery API Management
  const updateBatteryInfo = useCallback(async () => {
    if (!isBatteryApiSupported || !opts.enableBatteryOptimization) return;

    try {
      const battery = await (navigator as any).getBattery();
      setBatteryInfo({
        charging: battery.charging,
        level: battery.level,
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime
      });
    } catch (err) {
      console.warn('Failed to get battery info:', err);
    }
  }, [isBatteryApiSupported, opts.enableBatteryOptimization]);

  // Visibility API Management
  useEffect(() => {
    if (!opts.enableVisibilityHandling) return;

    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      setIsVisible(visible);

      if (visible) {
        console.log('Page became visible');
        // Re-acquire wake lock if it was released
        if (opts.enableWakeLock && !wakeLock) {
          requestWakeLock();
        }
      } else {
        console.log('Page became hidden');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [opts.enableVisibilityHandling, opts.enableWakeLock, wakeLock, requestWakeLock]);

  // Battery monitoring
  useEffect(() => {
    if (!opts.enableBatteryOptimization) return;

    updateBatteryInfo();

    // Set up battery event listeners if supported
    if (isBatteryApiSupported) {
      (navigator as any).getBattery().then((battery: any) => {
        const handleBatteryChange = () => updateBatteryInfo();

        battery.addEventListener('chargingchange', handleBatteryChange);
        battery.addEventListener('levelchange', handleBatteryChange);
        battery.addEventListener('chargingtimechange', handleBatteryChange);
        battery.addEventListener('dischargingtimechange', handleBatteryChange);

        return () => {
          battery.removeEventListener('chargingchange', handleBatteryChange);
          battery.removeEventListener('levelchange', handleBatteryChange);
          battery.removeEventListener('chargingtimechange', handleBatteryChange);
          battery.removeEventListener('dischargingtimechange', handleBatteryChange);
        };
      });
    }
  }, [opts.enableBatteryOptimization, isBatteryApiSupported, updateBatteryInfo]);

  // Heartbeat for background activity
  const startHeartbeat = useCallback((callback: () => void) => {
    heartbeatCallbackRef.current = callback;

    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (heartbeatCallbackRef.current) {
        heartbeatCallbackRef.current();
      }
    }, opts.heartbeatInterval);

    console.log(`Heartbeat started with ${opts.heartbeatInterval}ms interval`);
  }, [opts.heartbeatInterval]);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    heartbeatCallbackRef.current = null;
    console.log('Heartbeat stopped');
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      releaseWakeLock();
      stopHeartbeat();
    };
  }, [releaseWakeLock, stopHeartbeat]);

  return {
    isVisible,
    wakeLock,
    batteryInfo,
    requestWakeLock,
    releaseWakeLock,
    isWakeLockSupported,
    isBatteryApiSupported,
    startHeartbeat,
    stopHeartbeat
  };
};