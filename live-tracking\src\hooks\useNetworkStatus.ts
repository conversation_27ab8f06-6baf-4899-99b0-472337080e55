import { useState, useEffect, useCallback } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
}

export interface UseNetworkStatusReturn {
  networkStatus: NetworkStatus;
  isOnline: boolean;
  isSlowConnection: boolean;
  checkConnectivity: () => Promise<boolean>;
}

const getConnectionInfo = (): Partial<NetworkStatus> => {
  if (typeof navigator !== 'undefined' && 'connection' in navigator) {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        connectionType: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        isSlowConnection: connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g'
      };
    }
  }
  
  return {
    connectionType: 'unknown',
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
    isSlowConnection: false
  };
};

export const useNetworkStatus = (): UseNetworkStatusReturn => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(() => ({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    ...getConnectionInfo()
  } as NetworkStatus));

  const updateNetworkStatus = useCallback(() => {
    const isOnline = navigator.onLine;
    const connectionInfo = getConnectionInfo();
    
    setNetworkStatus(prev => ({
      ...prev,
      isOnline,
      ...connectionInfo
    }));
  }, []);

  const checkConnectivity = useCallback(async (): Promise<boolean> => {
    try {
      // Try to fetch a small resource to test actual connectivity
      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      return response.ok;
    } catch (error) {
      console.warn('Connectivity check failed:', error);
      return false;
    }
  }, []);

  useEffect(() => {
    const handleOnline = () => {
      console.log('Network: Online');
      updateNetworkStatus();
    };

    const handleOffline = () => {
      console.log('Network: Offline');
      updateNetworkStatus();
    };

    const handleConnectionChange = () => {
      console.log('Network: Connection changed');
      updateNetworkStatus();
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for connection changes if supported
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      if (connection) {
        connection.addEventListener('change', handleConnectionChange);
      }
    }

    // Initial status update
    updateNetworkStatus();

    // Periodic connectivity check
    const connectivityInterval = setInterval(async () => {
      if (navigator.onLine) {
        const isActuallyOnline = await checkConnectivity();
        if (!isActuallyOnline && networkStatus.isOnline) {
          console.log('Network: False positive online status detected');
          setNetworkStatus(prev => ({ ...prev, isOnline: false }));
        }
      }
    }, 30000); // Check every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (typeof navigator !== 'undefined' && 'connection' in navigator) {
        const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
        if (connection) {
          connection.removeEventListener('change', handleConnectionChange);
        }
      }
      
      clearInterval(connectivityInterval);
    };
  }, [updateNetworkStatus, checkConnectivity, networkStatus.isOnline]);

  return {
    networkStatus,
    isOnline: networkStatus.isOnline,
    isSlowConnection: networkStatus.isSlowConnection,
    checkConnectivity
  };
};
