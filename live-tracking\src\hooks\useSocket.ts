import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp?: string;
  receivedAt?: number;
}

export interface ConnectionCount {
  total: number;
  admins: number;
  trackers: number;
}

export interface UseSocketOptions {
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

export interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  connectionCount: ConnectionCount;
  joinAsAdmin: () => void;
  joinAsTracker: () => void;
  sendLocation: (location: LocationData) => void;
  disconnect: () => void;
  reconnect: () => void;
}

export const useSocket = (options: UseSocketOptions = {}): UseSocketReturn => {
  const {
    autoConnect = true,
    reconnection = true,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000
  } = options;

  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionCount, setConnectionCount] = useState<ConnectionCount>({
    total: 0,
    admins: 0,
    trackers: 0
  });

  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);

  useEffect(() => {
    if (!autoConnect) return;

    const socketInstance = io({
      reconnection,
      reconnectionAttempts,
      reconnectionDelay,
      transports: ['websocket', 'polling']
    });

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Socket connected:', socketInstance.id);
      setIsConnected(true);
      reconnectAttemptsRef.current = 0;
    });

    socketInstance.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setIsConnected(false);
      
      // Handle manual reconnection for certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'transport close') {
        handleReconnection(socketInstance);
      }
    });

    socketInstance.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setIsConnected(false);
      handleReconnection(socketInstance);
    });

    // Connection count updates
    socketInstance.on('connection-count', (count: ConnectionCount) => {
      setConnectionCount(count);
    });

    setSocket(socketInstance);

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      socketInstance.disconnect();
    };
  }, [autoConnect, reconnection, reconnectionAttempts, reconnectionDelay]);

  const handleReconnection = (socketInstance: Socket) => {
    if (reconnectAttemptsRef.current < reconnectionAttempts) {
      reconnectAttemptsRef.current++;
      const delay = reconnectionDelay * Math.pow(2, reconnectAttemptsRef.current - 1);
      
      console.log(`Attempting reconnection ${reconnectAttemptsRef.current}/${reconnectionAttempts} in ${delay}ms`);
      
      reconnectTimeoutRef.current = setTimeout(() => {
        if (!socketInstance.connected) {
          socketInstance.connect();
        }
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  };

  const joinAsAdmin = () => {
    if (socket && isConnected) {
      socket.emit('join-as-admin');
      console.log('Joined as admin');
    }
  };

  const joinAsTracker = () => {
    if (socket && isConnected) {
      socket.emit('join-as-tracker');
      console.log('Joined as tracker');
    }
  };

  const sendLocation = (location: LocationData) => {
    if (socket && isConnected) {
      socket.emit('location-update', location);
    }
  };

  const disconnect = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
    }
  };

  const reconnect = () => {
    if (socket) {
      reconnectAttemptsRef.current = 0;
      socket.connect();
    }
  };

  return {
    socket,
    isConnected,
    connectionCount,
    joinAsAdmin,
    joinAsTracker,
    sendLocation,
    disconnect,
    reconnect
  };
};
