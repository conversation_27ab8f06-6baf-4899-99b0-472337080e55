import { useState, useEffect, useCallback, useRef } from 'react'
import { locationService, LocationData } from '../../lib/supabase'
import { RealtimeChannel } from '@supabase/supabase-js'

interface LocationState {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: string
}

interface UseSupabaseLocationOptions {
  deviceId: string
  enableTracking?: boolean
  trackingInterval?: number
  enableRealtime?: boolean
}

interface UseSupabaseLocationReturn {
  // Current location state
  currentLocation: LocationState | null
  locationHistory: LocationData[]
  
  // Connection state
  isConnected: boolean
  isTracking: boolean
  error: string | null
  
  // Actions
  startTracking: () => void
  stopTracking: () => void
  sendLocation: (location: LocationState) => Promise<void>
  
  // Stats
  lastUpdateTime: Date | null
  updateCount: number
}

export function useSupabaseLocation({
  deviceId,
  enableTracking = false,
  trackingInterval = 5000,
  enableRealtime = true
}: UseSupabaseLocationOptions): UseSupabaseLocationReturn {
  
  // State
  const [currentLocation, setCurrentLocation] = useState<LocationState | null>(null)
  const [locationHistory, setLocationHistory] = useState<LocationData[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [isTracking, setIsTracking] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null)
  const [updateCount, setUpdateCount] = useState(0)
  
  // Refs
  const trackingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const realtimeSubscriptionRef = useRef<RealtimeChannel | null>(null)
  const watchIdRef = useRef<number | null>(null)

  // Send location to Supabase
  const sendLocation = useCallback(async (location: LocationState) => {
    try {
      setError(null)
      
      const result = await locationService.upsertLocation(deviceId, {
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy
      })
      
      setCurrentLocation(location)
      setLastUpdateTime(new Date())
      setUpdateCount(prev => prev + 1)
      setIsConnected(true)
      
      console.log('Location sent successfully:', result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send location'
      setError(errorMessage)
      setIsConnected(false)
      console.error('Error sending location:', err)
    }
  }, [deviceId])

  // Get current position from GPS
  const getCurrentPosition = useCallback((): Promise<LocationState> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location: LocationState = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          }
          resolve(location)
        },
        (error) => {
          reject(new Error(`Geolocation error: ${error.message}`))
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      )
    })
  }, [])

  // Start location tracking
  const startTracking = useCallback(async () => {
    if (isTracking) return

    try {
      setError(null)
      setIsTracking(true)

      // Send initial location
      const initialLocation = await getCurrentPosition()
      await sendLocation(initialLocation)

      // Set up continuous tracking
      trackingIntervalRef.current = setInterval(async () => {
        try {
          const location = await getCurrentPosition()
          await sendLocation(location)
        } catch (err) {
          console.error('Error in tracking interval:', err)
          setError(err instanceof Error ? err.message : 'Tracking error')
        }
      }, trackingInterval)

      console.log('Location tracking started')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start tracking')
      setIsTracking(false)
      console.error('Error starting tracking:', err)
    }
  }, [isTracking, getCurrentPosition, sendLocation, trackingInterval])

  // Stop location tracking
  const stopTracking = useCallback(() => {
    if (trackingIntervalRef.current) {
      clearInterval(trackingIntervalRef.current)
      trackingIntervalRef.current = null
    }
    
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current)
      watchIdRef.current = null
    }
    
    setIsTracking(false)
    console.log('Location tracking stopped')
  }, [])

  // Load initial data and set up real-time subscription
  useEffect(() => {
    let mounted = true

    const initialize = async () => {
      try {
        // Load latest location
        const latestLocation = await locationService.getLatestLocation(deviceId)
        if (mounted && latestLocation) {
          setCurrentLocation({
            latitude: latestLocation.latitude,
            longitude: latestLocation.longitude,
            accuracy: latestLocation.accuracy,
            timestamp: latestLocation.timestamp
          })
        }

        // Load location history
        const history = await locationService.getLocationHistory(deviceId, 50)
        if (mounted) {
          setLocationHistory(history)
        }

        setIsConnected(true)
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to load initial data')
          setIsConnected(false)
        }
      }
    }

    initialize()

    // Set up real-time subscription
    if (enableRealtime) {
      realtimeSubscriptionRef.current = locationService.subscribeToDeviceUpdates(
        deviceId,
        (payload) => {
          if (!mounted) return

          console.log('Real-time update received:', payload)
          
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            const newLocation = payload.new as LocationData
            
            setCurrentLocation({
              latitude: newLocation.latitude,
              longitude: newLocation.longitude,
              accuracy: newLocation.accuracy,
              timestamp: newLocation.timestamp
            })
            
            setLocationHistory(prev => [newLocation, ...prev.slice(0, 49)])
            setLastUpdateTime(new Date())
            setIsConnected(true)
          }
        }
      )
    }

    return () => {
      mounted = false
      if (realtimeSubscriptionRef.current) {
        realtimeSubscriptionRef.current.unsubscribe()
      }
    }
  }, [deviceId, enableRealtime])

  // Auto-start tracking if enabled
  useEffect(() => {
    if (enableTracking && !isTracking) {
      startTracking()
    }
  }, [enableTracking, isTracking, startTracking])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTracking()
      if (realtimeSubscriptionRef.current) {
        realtimeSubscriptionRef.current.unsubscribe()
      }
    }
  }, [stopTracking])

  return {
    currentLocation,
    locationHistory,
    isConnected,
    isTracking,
    error,
    startTracking,
    stopTracking,
    sendLocation,
    lastUpdateTime,
    updateCount
  }
}
