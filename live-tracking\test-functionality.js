const { io } = require('socket.io-client');

console.log('🧪 Testing Live Tracking Application Functionality\n');

// Test configuration
const SERVER_URL = 'http://localhost:3000';
const TEST_LOCATION = {
  lat: 12.9716,
  lng: 77.5946,
  accuracy: 10
};

let adminSocket, trackerSocket;
let testResults = {
  serverConnection: false,
  adminConnection: false,
  trackerConnection: false,
  locationBroadcast: false,
  realTimeUpdates: false
};

async function runTests() {
  console.log('1️⃣ Testing server connectivity...');
  
  try {
    // Test admin connection
    adminSocket = io(SERVER_URL, {
      timeout: 5000,
      forceNew: true
    });

    adminSocket.on('connect', () => {
      console.log('✅ Admin socket connected');
      testResults.adminConnection = true;
      
      // Join as admin
      adminSocket.emit('join', 'admin');
      console.log('📡 Admin joined the room');
    });

    adminSocket.on('connect_error', (error) => {
      console.log('❌ Admin connection failed:', error.message);
    });

    // Wait for admin connection
    await new Promise(resolve => {
      adminSocket.on('connect', resolve);
      setTimeout(() => resolve(), 3000);
    });

    if (!testResults.adminConnection) {
      console.log('❌ Admin connection test failed');
      return;
    }

    console.log('\n2️⃣ Testing tracker connection...');
    
    // Test tracker connection
    trackerSocket = io(SERVER_URL, {
      timeout: 5000,
      forceNew: true
    });

    trackerSocket.on('connect', () => {
      console.log('✅ Tracker socket connected');
      testResults.trackerConnection = true;
      
      // Join as tracker
      trackerSocket.emit('join', 'tracker');
      console.log('📍 Tracker joined the room');
    });

    trackerSocket.on('connect_error', (error) => {
      console.log('❌ Tracker connection failed:', error.message);
    });

    // Wait for tracker connection
    await new Promise(resolve => {
      trackerSocket.on('connect', resolve);
      setTimeout(() => resolve(), 3000);
    });

    if (!testResults.trackerConnection) {
      console.log('❌ Tracker connection test failed');
      return;
    }

    console.log('\n3️⃣ Testing location broadcast...');
    
    // Set up location listener on tracker
    trackerSocket.on('location-update', (locationData) => {
      console.log('✅ Location received by tracker:', locationData);
      testResults.realTimeUpdates = true;
    });

    // Send location from admin
    setTimeout(() => {
      console.log('📤 Sending test location from admin...');
      adminSocket.emit('location-update', TEST_LOCATION);
      testResults.locationBroadcast = true;
    }, 1000);

    // Wait for location update
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n4️⃣ Testing connection count updates...');
    
    adminSocket.on('connection-count', (count) => {
      console.log('📊 Connection count received:', count);
    });

    trackerSocket.on('connection-count', (count) => {
      console.log('📊 Connection count received:', count);
    });

    // Wait for connection count updates
    await new Promise(resolve => setTimeout(resolve, 2000));

  } catch (error) {
    console.log('❌ Test error:', error.message);
  } finally {
    // Clean up connections
    if (adminSocket) adminSocket.disconnect();
    if (trackerSocket) trackerSocket.disconnect();
    
    // Print test results
    console.log('\n📋 Test Results Summary:');
    console.log('========================');
    console.log(`Admin Connection: ${testResults.adminConnection ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Tracker Connection: ${testResults.trackerConnection ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Location Broadcast: ${testResults.locationBroadcast ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Real-time Updates: ${testResults.realTimeUpdates ? '✅ PASS' : '❌ FAIL'}`);
    
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const totalTests = Object.keys(testResults).length;
    
    console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! The application is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the server and connections.');
    }
    
    process.exit(0);
  }
}

// Start tests
runTests().catch(console.error);
