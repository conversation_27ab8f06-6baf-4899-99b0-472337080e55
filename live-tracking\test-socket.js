// Simple Socket.IO test script
const { io } = require('socket.io-client');

console.log('Testing Socket.IO connection...');

const socket = io('http://localhost:3000');

socket.on('connect', () => {
  console.log('✅ Connected to server:', socket.id);
  
  // Test joining as admin
  socket.emit('join-as-admin');
  console.log('📱 Joined as admin');
  
  // Test sending location data
  const testLocation = {
    latitude: 40.7128,
    longitude: -74.0060,
    accuracy: 10,
    timestamp: new Date().toISOString()
  };
  
  socket.emit('location-update', testLocation);
  console.log('📍 Sent test location:', testLocation);
});

socket.on('connection-count', (count) => {
  console.log('👥 Connection count:', count);
});

socket.on('location-update', (location) => {
  console.log('📍 Received location update:', location);
});

socket.on('disconnect', (reason) => {
  console.log('❌ Disconnected:', reason);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error);
});

// Test for 10 seconds then disconnect
setTimeout(() => {
  console.log('🔚 Test completed, disconnecting...');
  socket.disconnect();
  process.exit(0);
}, 10000);
